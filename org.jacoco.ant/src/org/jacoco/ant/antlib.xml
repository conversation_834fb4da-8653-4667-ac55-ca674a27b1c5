<?xml version="1.0"?>

<!-- 
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html
  
   Contributors:
      <PERSON> - initial API and implementation
-->

<antlib>
   <taskdef name="coverage" classname="org.jacoco.ant.CoverageTask"/>
   <taskdef name="agent" classname="org.jacoco.ant.AgentTask"/>
   <taskdef name="report" classname="org.jacoco.ant.ReportTask"/>
   <taskdef name="merge" classname="org.jacoco.ant.MergeTask"/>
   <taskdef name="dump" classname="org.jacoco.ant.DumpTask"/>
   <taskdef name="instrument" classname="org.jacoco.ant.InstrumentTask"/>
</antlib>
