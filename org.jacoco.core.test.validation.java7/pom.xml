<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.core.test.validation</artifactId>
    <version>0.8.4</version>
    <relativePath>../org.jacoco.core.test.validation</relativePath>
  </parent>

  <artifactId>org.jacoco.core.test.validation.java7</artifactId>

  <name>JaCoCo :: Test :: Core :: Validation Java 7</name>

  <properties>
    <bytecode.version>7</bytecode.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core.test</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>
</project>
