<?xml version="1.0" encoding="UTF-8"?>

<!-- 
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html
  
   Contributors:
      <PERSON> - initial API and implementation
-->

<project name="JaCoCo Dump Task Tests" xmlns:au="antlib:org.apache.ant.antunit" xmlns:jacoco="antlib:org.jacoco.ant">

	<target name="setUp">
		<tempfile property="temp.dir" prefix="jacocoTest" destdir="${java.io.tmpdir}" />
		<mkdir dir="${temp.dir}"/>
		<property name="exec.file" location="${temp.dir}/exec.file" />
	</target>

	<target name="tearDown">
		<delete dir="${temp.dir}" quiet="false" failonerror="true"/>
	</target>
	
	
	<target name="testNoServer">
		<au:expectfailure expectedMessage="Unable to dump coverage data">
			<jacoco:dump dump="true" destfile="${exec.file}" retryCount="0"/>
		</au:expectfailure>
		<au:assertFileDoesntExist file="${exec.file}"/>
	</target>
	
	<target name="testUnknownHost">
		<au:expectfailure expectedMessage="Unable to dump coverage data">
			<!--
			Note that malformed literal IPv6 address is used
			to cause UnknownHostException from java.net.InetAddress.getByName
			without DNS request.
			-->
			<jacoco:dump dump="false" address="[nosuchhost]"/>
		</au:expectfailure>
		<au:assertFileDoesntExist file="${exec.file}"/>
	</target>

	<target name="testInvalidPort">
		<au:expectfailure expectedMessage="Invalid port value">
			<jacoco:dump dump="false" port="-1234"/>
		</au:expectfailure>
		<au:assertFileDoesntExist file="${exec.file}"/>
	</target>
	
	<target name="testNoDestFile">
		<au:expectfailure expectedMessage="Destination file is required when dumping execution data">
			<jacoco:dump dump="true"/>
		</au:expectfailure>
		<au:assertFileDoesntExist file="${exec.file}"/>
	</target>
	
</project>
