<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.build</artifactId>
    <version>0.8.4</version>
    <relativePath>../org.jacoco.build</relativePath>
  </parent>

  <artifactId>org.jacoco.cli</artifactId>

  <name>JaCoCo :: Command Line Interface</name>
  <description>JaCoCo Command Line Interface</description>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.report</artifactId>
    </dependency>
    <dependency>
      <groupId>args4j</groupId>
      <artifactId>args4j</artifactId>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>exec</goal>
            </goals>
            <configuration>
              <toolchain>jdk</toolchain>
              <executable>java</executable>
              <arguments>
                <argument>-cp</argument>
                <classpath/>
                <argument>org.jacoco.cli.internal.XmlDocumentation</argument>
                <argument>${project.build.directory}/generated-documentation/cli.xml</argument>
              </arguments>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <shadedArtifactAttached>true</shadedArtifactAttached>
              <shadedClassifierName>nodeps</shadedClassifierName>
              <minimizeJar>true</minimizeJar>
              <relocations>
                <relocation>
                  <pattern>org.objectweb.asm</pattern>
                  <shadedPattern>org.jacoco.cli.internal.asm</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.kohsuke.args4j</pattern>
                  <shadedPattern>org.jacoco.cli.internal.args4j</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.jacoco.core</pattern>
                  <shadedPattern>org.jacoco.cli.internal.core</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>org.jacoco.report</pattern>
                  <shadedPattern>org.jacoco.cli.internal.report</shadedPattern>
                </relocation>
              </relocations>
              <transformers>
                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                  <manifestEntries>
                    <Implementation-Title>${project.description}</Implementation-Title>
                    <Implementation-Vendor>${project.organization.name}</Implementation-Vendor>
                    <Implementation-Version>${project.version}</Implementation-Version>
                    <Main-Class>org.jacoco.cli.internal.Main</Main-Class>
                  </manifestEntries>
                </transformer>
              </transformers>
              <filters>
                <filter>
                  <artifact>args4j:args4j</artifact>
                  <excludes>
                    <exclude>**/Messages_*.properties</exclude>
                  </excludes>
                </filter>
                <filter>
                  <artifact>org.ow2.asm:*</artifact>
                  <excludes>
                    <exclude>module-info.class</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
