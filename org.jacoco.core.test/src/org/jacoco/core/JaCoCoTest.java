/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R<PERSON> Hoffmann - initial API and implementation
 *    
 *******************************************************************************/
package org.jacoco.core;

import static org.junit.Assert.assertNotNull;

import org.junit.Test;

/**
 * Unit tests for {@link JaCoCo}.
 */
public class JaCoCoTest {

	@Test
	public void testVERSION() {
		assertNotNull(JaCoCo.VERSION);
	}

	@Test
	public void testHOMEURL() {
		assertNotNull(JaCoCo.HOMEURL);
	}

	@Test
	public void testRUNTIMEPACKAGE() {
		assertNotNull(JaCoCo.RUNTIMEPACKAGE);
	}

}
