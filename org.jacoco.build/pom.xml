<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.jacoco</groupId>
  <artifactId>org.jacoco.build</artifactId>
  <version>0.8.4</version>
  <packaging>pom</packaging>

  <name>JaCoCo</name>
  <description>JaCoCo - Java Code Coverage Library</description>
  <url>http://jacoco.org</url>
  <inceptionYear>2009</inceptionYear>
  <organization>
    <name>Mountainminds GmbH &amp; Co. KG</name>
  </organization>
  <licenses>
    <license>
      <name>Eclipse Public License v1.0</name>
      <url>http://www.eclipse.org/legal/epl-v10.html</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>mtnminds</id>
      <name>Marc R. Hoffmann</name>
      <email><EMAIL></email>
      <timezone>+1</timezone>
      <roles>
        <role>Project Lead</role>
      </roles>
    </developer>
    <developer>
      <id>brock_j</id>
      <name>Brock Janiczak</name>
      <email><EMAIL></email>
      <timezone>+10</timezone>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
    <developer>
      <id>mandrikov</id>
      <name>Evgeny Mandrikov</name>
      <email><EMAIL></email>
      <url>http://godin.net.ru</url>
      <organization>SonarSource</organization>
      <organizationUrl>http://www.sonarsource.com</organizationUrl>
      <timezone>+3</timezone>
      <roles>
        <role>Build and release manager</role>
      </roles>
    </developer>
    <developer>
      <id>mfriedenhagen</id>
      <name>Mirko Friedenhagen</name>
      <email><EMAIL></email>
      <timezone>+1</timezone>
      <roles>
        <role>Developer</role>
      </roles>
    </developer>
  </developers>

  <contributors>
    <contributor>
      <name>Radek Liba</name>
    </contributor>
    <contributor>
      <name>Christoph Beck</name>
    </contributor>
  </contributors>

  <modules>
    <!-- Order is important: org.jacoco.agent.rt embeds into org.jacoco.agent and JaCoCo Agent used during tests -->
    <module>../org.jacoco.core</module>
    <module>../org.jacoco.report</module>
    <module>../org.jacoco.agent.rt</module>
    <module>../org.jacoco.agent</module>
    <module>../org.jacoco.ant</module>
    <module>../org.jacoco.cli</module>
    <module>../org.jacoco.examples</module>
    <module>../jacoco-maven-plugin</module>

    <module>../org.jacoco.tests</module>

    <module>../org.jacoco.doc</module>
    <module>../jacoco</module>
  </modules>

  <scm>
    <connection>scm:git:git://github.com/jacoco/jacoco.git</connection>
    <developerConnection>scm:git:ssh://**************:jacoco/jacoco.git</developerConnection>
    <url>https://github.com/jacoco/jacoco</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/jacoco/jacoco/issues</url>
  </issueManagement>
  <distributionManagement>
    <repository>
      <id>sonatype-nexus-staging</id>
      <name>Nexus Release Repository</name>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>${sonatypeOssDistMgmtSnapshotsUrl}</url>
    </snapshotRepository>
  </distributionManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <sonatypeOssDistMgmtSnapshotsUrl>https://oss.sonatype.org/content/repositories/snapshots/</sonatypeOssDistMgmtSnapshotsUrl>

    <maven.build.timestamp.format>yyyyMMddhhmm</maven.build.timestamp.format>
    <jacoco.home.url>http://www.jacoco.org/jacoco</jacoco.home.url>
    <copyright.years>${project.inceptionYear}, 2019</copyright.years>

    <bytecode.version>1.5</bytecode.version>
    <maven.compiler.source>${bytecode.version}</maven.compiler.source>
    <maven.compiler.target>${bytecode.version}</maven.compiler.target>

    <jvm.args></jvm.args>
    <argLine>${jvm.args}</argLine>

    <!-- Dependencies versions -->
    <asm.version>7.1</asm.version>
    <ant.version>1.7.1</ant.version>
    <args4j.version>2.0.28</args4j.version>
    <junit.version>4.8.2</junit.version>

    <!-- ================== -->
    <!-- For SonarQube analysis -->
    <!-- ================== -->
    <sonar.jacoco.reportPath>../${project.artifactId}.test/target/jacoco.exec</sonar.jacoco.reportPath>
    <sonar.surefire.reportsPath>../${project.artifactId}.test/target/surefire-reports/</sonar.surefire.reportsPath>

    <!-- See http://jira.codehaus.org/browse/SONAR-2096 -->
    <sonar.java.source>1.5</sonar.java.source>
    <sonar.java.target>1.5</sonar.java.target>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- Project dependencies -->
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.core</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.report</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.agent</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.agent</artifactId>
        <classifier>runtime</classifier>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.agent.rt</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.ant</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.ant</artifactId>
        <classifier>nodeps</classifier>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.cli</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>${project.groupId}</groupId>
        <artifactId>org.jacoco.examples</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- Third-party dependencies -->
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-commons</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-tree</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-analysis</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-util</artifactId>
        <version>${asm.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ant</groupId>
        <artifactId>ant</artifactId>
        <version>${ant.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ant</groupId>
        <artifactId>ant-junit</artifactId>
        <version>${ant.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.ant</groupId>
        <artifactId>ant-antunit</artifactId>
        <version>1.2</version>
      </dependency>
      <dependency>
        <groupId>args4j</groupId>
        <artifactId>args4j</artifactId>
        <version>${args4j.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <resources>
      <resource>
        <directory>src</directory>
        <filtering>false</filtering>
        <excludes>
          <exclude>**/*.java</exclude>
          <exclude>**/*.kt</exclude>
          <exclude>**/*.groovy</exclude>
          <exclude>**/*.properties</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src</directory>
        <filtering>true</filtering>
        <includes>
          <include>**/*.properties</include>
        </includes>
      </resource>
      <resource>
        <directory>.</directory>
        <filtering>true</filtering>
        <includes>
          <include>about.html</include>
        </includes>
      </resource>
    </resources>

    <pluginManagement>
      <plugins>
        <!-- Apache plugins -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>1.6</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>2.2.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>2.4.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>3.7.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>2.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>2.8.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>3.0.0-M2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-invoker-plugin</artifactId>
          <version>2.0.0</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>1.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>2.3.1</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <!--
          Do not upgrade to 3.1.0 because it has troubles with "Automatic-Module-Name"
          See https://issues.apache.org/jira/browse/MJAVADOC-588
          -->
          <version>3.0.1</version>
          <configuration>
            <quiet>true</quiet>
            <detectOfflineLinks>false</detectOfflineLinks>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>3.6.0</version>
          <dependencies>
            <dependency>
              <!-- Workaround to be able to compile into Java 13 bytecode -->
              <groupId>org.ow2.asm</groupId>
              <artifactId>asm</artifactId>
              <version>7.1</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>2.1</version>
          <configuration>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <mavenExecutorId>forked-path</mavenExecutorId> 
            <useReleaseProfile>false</useReleaseProfile>
            <!--
            Allows to activate release profile during release.
            We don't use releaseProfiles parameter, because it affects only release:perform goal
            -->
            <arguments>-Prelease</arguments>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>2.5</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>3.2.1</version>
          <dependencies>
            <dependency>
              <!-- Workaround to be able to compile into Java 13 bytecode -->
              <groupId>org.ow2.asm</groupId>
              <artifactId>asm</artifactId>
              <version>7.1</version>
            </dependency>
          </dependencies>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>2.1.2</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.9</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>3.3</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-toolchains-plugin</artifactId>
          <version>1.0</version>
        </plugin>
        <!-- Mojo plugins -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>1.6</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>1.5</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>buildnumber-maven-plugin</artifactId>
          <version>1.2</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>xml-maven-plugin</artifactId>
          <version>1.0</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>exec-maven-plugin</artifactId>
          <!--  latest version which runs with Java 5 -->
          <version>1.5.0</version>
        </plugin>
        <!-- Third-party plugins -->
        <plugin>
          <groupId>com.github.genthaler</groupId>
          <artifactId>beanshell-maven-plugin</artifactId>
          <version>1.4</version>
        </plugin>
        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <version>3.5.1</version>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <testSourceDirectory>${project.build.sourceDirectory}</testSourceDirectory>
          <testClassesDirectory>${project.build.outputDirectory}</testClassesDirectory>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <configuration>
          <!-- We can't use delimiters "${*}" and "$*$" together - probably it's bug in plugin -->
          <!-- so for now we will define only our delimiters in compliance with https://sourceforge.net/apps/trac/eclemma/changeset/1229 -->
          <!-- but it would be better to use defaults -->
          <useDefaultDelimiters>false</useDefaultDelimiters>
          <delimiters>
            <delimiter>@*@</delimiter>
            <delimiter>$*$</delimiter>
          </delimiters>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-shade-plugin</artifactId>
        <configuration>
          <filters>
            <!-- Don't include signatures -->
            <filter>
              <artifact>*:*</artifact>
              <excludes>
                <exclude>META-INF/*.SF</exclude>
                <exclude>META-INF/*.DSA</exclude>
                <exclude>META-INF/*.RSA</exclude>
              </excludes>
            </filter>
          </filters>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <configuration>
          <archiverConfig>
            <!-- Workaround for http://jira.codehaus.org/browse/MASSEMBLY-422 -->
            <!-- 420(dec) = 644(oct) -->
            <fileMode>420</fileMode>
            <!-- 493(dec) = 755(oct) -->
            <directoryMode>493</directoryMode>
            <defaultDirectoryMode>493</defaultDirectoryMode>
          </archiverConfig>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce</id>
            <phase>validate</phase>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <requireJavaVersion>
                  <version>1.8</version>
                </requireJavaVersion>
                <requireMavenVersion>
                  <version>3.3.9</version>
                </requireMavenVersion>
                <requireNoRepositories>
                  <message>The rules for repo1.maven.org are that pom.xml files should not include repository definitions.</message>
                  <banRepositories>true</banRepositories>
                  <banPluginRepositories>true</banPluginRepositories>
                </requireNoRepositories>
                <requireReleaseDeps>
                  <message>No SNAPSHOT versions allowed for dependencies</message>
                  <onlyWhenRelease>true</onlyWhenRelease>
                </requireReleaseDeps>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>check-license-header</id>
            <phase>validate</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <fileset dir="${basedir}" includes="**/*.java,**/*.xml,**/*.bsh" excludes="target/**,.idea/**,nb-configuration.xml" id="missinglicense.fileset">
                  <include name="**/*.kt"/>
                  <include name="**/*.groovy"/>
                  <not>
                    <and>
                      <contains text="Copyright (c) 2009, 2019 Mountainminds GmbH &amp; Co. KG and Contributors"/>
                      <contains text="All rights reserved. This program and the accompanying materials"/>
                      <contains text="are made available under the terms of the Eclipse Public License v1.0"/>
                      <contains text="which accompanies this distribution, and is available at"/>
                      <contains text="http://www.eclipse.org/legal/epl-v10.html"/>
                    </and>
                  </not>
                </fileset>
                <pathconvert property="missing" refid="missinglicense.fileset"/>
                <fail message="Invalid license info in: ${missing}">
                  <condition>
                    <not>
                      <equals arg1="${missing}" arg2=""/>
                    </not>
                  </condition>
                </fail>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>parse-version</id>
            <phase>validate</phase>
            <goals>
              <goal>parse-version</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>buildnumber-maven-plugin</artifactId>
        <executions>
          <execution>
            <phase>validate</phase>
            <goals>
              <goal>create</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <doCheck>false</doCheck>
          <doUpdate>false</doUpdate>
          <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
          <revisionOnScmFailure>0000000</revisionOnScmFailure>
        </configuration>
      </plugin>

      <plugin>
        <groupId>com.github.genthaler</groupId>
        <artifactId>beanshell-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>parse-version</id>
            <phase>validate</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <quiet>true</quiet>
              <script><![CDATA[
                major = project.getProperties().get("parsedVersion.majorVersion");
                minor = project.getProperties().get("parsedVersion.minorVersion");
                incremental = project.getProperties().get("parsedVersion.incrementalVersion");
                unqualifiedVersion = major + "." + minor + "." + incremental;
                project.getProperties().setProperty("unqualifiedVersion", unqualifiedVersion);

                qualifier = "${maven.build.timestamp}";
                project.getProperties().setProperty("buildQualifier", qualifier);

                qualifiedVersion = unqualifiedVersion + "." + qualifier;
                project.getProperties().setProperty("qualified.bundle.version", qualifiedVersion);

                buildDate = qualifier.substring(0, 4) + "/" + qualifier.substring(4, 6) + "/" + qualifier.substring(6, 8);
                project.getProperties().setProperty("build.date", buildDate);

                buildNumber = project.getProperties().get("buildNumber");
                pkgName = buildNumber.substring(buildNumber.length() - 7, buildNumber.length());
                project.getProperties().setProperty("jacoco.runtime.package.name", "org.jacoco.agent.rt.internal_" + pkgName);
              ]]>
              </script>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <configuration>
          <instructions>
            <Automatic-Module-Name>${project.artifactId}</Automatic-Module-Name>
            <Bundle-Version>${qualified.bundle.version}</Bundle-Version>
            <Bundle-Name>${project.description}</Bundle-Name>
            <Export-Package>
              !about.html,
              *.internal*;x-internal:=true;version="${version;===;${Bundle-Version}}",
              *;version="${version;===;${Bundle-Version}}"
            </Export-Package>
            <Import-Package>
              org.jacoco.*;version="${range;[===,==+);${Bundle-Version}}",
              org.objectweb.asm.*;version="${range;[===,=+);${asm.version}}"
            </Import-Package>
            <Bundle-RequiredExecutionEnvironment>J2SE-1.5</Bundle-RequiredExecutionEnvironment>
            <Eclipse-SourceReferences>scm:git:git://github.com/jacoco/jacoco.git;path="${project.artifactId}";commitId=${buildNumber}</Eclipse-SourceReferences>
          </instructions>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <!-- http://openjdk.java.net/jeps/182 -->
    <profile>
      <id>maven-jdk8</id>
      <activation>
        <jdk>[8,12)</jdk>
      </activation>
      <properties>
        <bytecode.version>6</bytecode.version>
      </properties>
    </profile>

    <!--<profile>-->
      <!--<id>maven-jdk12</id>-->
      <!--<activation>-->
        <!--<jdk>[12,)</jdk>-->
      <!--</activation>-->
      <!--<properties>-->
        <!--<bytecode.version>7</bytecode.version>-->
      <!--</properties>-->
    <!--</profile>-->

    <!-- This profile enables use of JDK from Maven Toolchains -->
    <profile>
      <id>integration-tests</id>
      <activation>
        <property>
          <name>jdk.version</name>
        </property>
      </activation>
      <build>
        <plugins>
          <!-- See http://maven.apache.org/guides/mini/guide-using-toolchains.html -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-toolchains-plugin</artifactId>
            <executions>
              <execution>
                <phase>validate</phase>
                <goals>
                  <goal>toolchain</goal>
                </goals>
              </execution>
            </executions>
            <configuration>
              <toolchains>
                <jdk>
                  <version>${jdk.version}</version>
                </jdk>
              </toolchains>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- This profile enables use of ECJ -->
    <profile>
      <id>ecj</id>
      <activation>
        <property>
          <name>ecj</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <compilerId>eclipse</compilerId>
              <source>1.8</source>
              <target>1.8</target>
            </configuration>
            <dependencies>
              <dependency>
                <groupId>org.eclipse.jdt.core.compiler</groupId>
                <artifactId>ecj</artifactId>
                <version>4.6.1</version>
              </dependency>
              <dependency>
                <groupId>org.codehaus.plexus</groupId>
                <artifactId>plexus-compiler-eclipse</artifactId>
                <version>2.8.1</version>
              </dependency>
            </dependencies>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!--
    Profiles for different JDK versions:
    -->

    <profile>
      <id>jdk6</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>6</value>
        </property>
      </activation>
      <properties>
        <jvm.args>-XX:-FailOverToOldVerifier -Xverify:all</jvm.args>
      </properties>
    </profile>

    <profile>
      <id>jdk7</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>7</value>
        </property>
      </activation>
      <properties>
        <jvm.args>-XX:-FailOverToOldVerifier -Xverify:all</jvm.args>
      </properties>
    </profile>

    <profile>
      <id>jdk8</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>8</value>
        </property>
      </activation>
      <properties>
        <jvm.args>-XX:-FailOverToOldVerifier -Xverify:all</jvm.args>
      </properties>
    </profile>

    <profile>
      <id>jdk9</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>9</value>
        </property>
      </activation>
      <properties>
        <bytecode.version>6</bytecode.version>
      </properties>
    </profile>

    <profile>
      <id>jdk10</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>10</value>
        </property>
      </activation>
      <properties>
        <bytecode.version>6</bytecode.version>
      </properties>
    </profile>

    <profile>
      <id>jdk11</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>11</value>
        </property>
      </activation>
      <properties>
        <bytecode.version>6</bytecode.version>
      </properties>
    </profile>

    <profile>
      <id>jdk12</id>
      <activation>
        <property>
          <name>jdk.version</name>
          <value>12</value>
        </property>
      </activation>
      <properties>
        <bytecode.version>7</bytecode.version>
      </properties>
    </profile>

    <!-- This profile enables generation of JARs with sources and javadocs -->
    <profile>
      <id>sources</id>
      <activation>
        <file>
          <exists>src/</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <!-- Generates jar with sources -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-sources</id>
                <phase>verify</phase>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- Generates Javadoc -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- This profile is activated when a project is released. -->
    <profile>
      <id>release</id>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <inherited>false</inherited>
            <executions>
              <execution>
                <id>enforce-release-rules</id>
                <phase>verify</phase>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <requireReleaseVersion/>
                    <requireProperty>
                      <property>buildNumber</property>
                      <regex>[0-9a-f]{40}</regex>
                    </requireProperty>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!-- Checks compatibility with Java API -->
          <plugin>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>animal-sniffer-maven-plugin</artifactId>
            <executions>
              <execution>
                <id>enforce-java-api-compatibility</id>
                <phase>verify</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <signature>
                    <groupId>org.codehaus.mojo.signature</groupId>
                    <artifactId>java15</artifactId>
                    <version>1.0</version>
                  </signature>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <!-- Signs artifacts -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- This profile is active only when used from Eclipse m2e and is used only to store settings. -->
    <profile>
      <id>m2e</id>
      <activation>
        <property>
          <name>m2e.version</name>
        </property>
      </activation>
      <build>
        <pluginManagement>
          <plugins>
            <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
            <plugin>
              <groupId>org.eclipse.m2e</groupId>
              <artifactId>lifecycle-mapping</artifactId>
              <version>1.0.0</version>
              <configuration>
                <lifecycleMappingMetadata>
                  <pluginExecutions>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>run</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-invoker-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>install</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>unpack</goal>
                          <goal>copy-dependencies</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.codehaus.groovy.maven</groupId>
                        <artifactId>gmaven-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>execute</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>xml-maven-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>transform</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>prepare-agent</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-enforcer-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>enforce</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                    <pluginExecution>
                      <pluginExecutionFilter>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>build-helper-maven-plugin</artifactId>
                        <versionRange>[0,)</versionRange>
                        <goals>
                          <goal>parse-version</goal>
                        </goals>
                      </pluginExecutionFilter>
                      <action>
                        <ignore></ignore>
                      </action>
                    </pluginExecution>
                  </pluginExecutions>
                </lifecycleMappingMetadata>
              </configuration>
            </plugin>
          </plugins>
        </pluginManagement>
      </build>
    </profile>
  </profiles>

</project>
