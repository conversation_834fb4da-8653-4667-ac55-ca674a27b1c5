<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.core.test.validation</artifactId>
    <version>0.8.4</version>
    <relativePath>../org.jacoco.core.test.validation</relativePath>
  </parent>

  <artifactId>org.jacoco.core.test.validation.groovy</artifactId>

  <name>JaCoCo :: Test :: Core :: Validation Groovy</name>

  <properties>
    <gmaven.version>1.6.2</gmaven.version>
    <groovy.version>2.5.3</groovy.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core.test</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.codehaus.groovy</groupId>
      <artifactId>groovy</artifactId>
      <version>${groovy.version}</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.gmavenplus</groupId>
        <artifactId>gmavenplus-plugin</artifactId>
        <version>${gmaven.version}</version>
        <executions>
          <execution>
            <id>compile</id>
            <phase>process-sources</phase>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <targetBytecode>${groovy.targetBytecode}</targetBytecode>
          <sources>
            <source>
              <directory>${project.build.sourceDirectory}</directory>
              <includes>
                <include>**/*.groovy</include>
              </includes>
            </source>
          </sources>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
