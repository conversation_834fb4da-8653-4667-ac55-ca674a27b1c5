/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R. Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.report.util;

import java.sql.*;

/**
 * 数据库连接工具
 */
public class JDBCUtils {

    private static Connection conn = null;
    public static Connection getConn(){
        PropertiesUtil.loadFile("jdbc.properties");
        String driver = PropertiesUtil.getPropertyValue("driver");
        String url = PropertiesUtil.getPropertyValue("url");
        String username  = PropertiesUtil.getPropertyValue("username");
        String password = PropertiesUtil.getPropertyValue("password");
        try {
            Class.forName(driver);
            conn = DriverManager.getConnection(url,username,password);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
            close();
        }
        return conn;
    }

    public static void close(){
        try {
            conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        //SQL语句
        String sql = "select package from jacoco_method_info where id = 1";
        Connection conn = getConn();
        Statement stmt = null;
        ResultSet ret = null;
        String packageName = null;
        try {
            stmt = conn.createStatement();
            //执行语句，得到结果集
            ret = stmt.executeQuery(sql);
            while (ret.next()) {
                //这里只查询的密码
                packageName = ret.getString(1);
//                System.out.println(packageName);
            }
            ret.close();
            conn.close();//关闭连接
        } catch (SQLException e1) {
            e1.printStackTrace();
        }
    }

    public static void insertMethodInfo(MethodInfo methodInfo) {
        Connection conn = getConn();
        int i = 0;
        String sql = "insert into jacoco_method_info (package,class,method,first_line,last_line,covered_ratio) values(?,?,?,?,?,?)";
        PreparedStatement pstmt;
        try {
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, methodInfo.getPackageName());
            pstmt.setString(2, methodInfo.getClassName());
            pstmt.setString(3, methodInfo.getMethodName());
            pstmt.setInt(4, methodInfo.getFirstLine());
            pstmt.setInt(5, methodInfo.getLastLine());
            pstmt.setDouble(6, methodInfo.getCoveredRatio());
            i = pstmt.executeUpdate();
            pstmt.close();
            conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }

    }

    public static void inserLineInfo(LineInfo lineInfo) {
        Connection conn = getConn();
        int i = 0;
        String sql = "insert into jacoco_line_info (package,class,line,status) values(?,?,?,?)";
        PreparedStatement pstmt;
        try {
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, lineInfo.getPackageName());
            pstmt.setString(2, lineInfo.getClassName());
            pstmt.setInt(3, lineInfo.getLine());
            pstmt.setInt(4, lineInfo.getStatus());
            i = pstmt.executeUpdate();
            pstmt.close();
            conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }

    }
}

