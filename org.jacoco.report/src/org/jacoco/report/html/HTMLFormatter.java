/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R<PERSON> Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.report.html;

import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.util.*;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.jacoco.core.analysis.CoverageBuilder;
import org.jacoco.core.analysis.IBundleCoverage;
import org.jacoco.core.analysis.ICoverageNode.CounterEntity;
import org.jacoco.core.data.ExecutionData;
import org.jacoco.core.data.SessionInfo;
import org.jacoco.core.internal.diff.ClassInfo;
import org.jacoco.report.ILanguageNames;
import org.jacoco.report.IMultiReportOutput;
import org.jacoco.report.IReportGroupVisitor;
import org.jacoco.report.IReportVisitor;
import org.jacoco.report.ISourceFileLocator;
import org.jacoco.report.JavaNames;
import org.jacoco.report.internal.ReportOutputFolder;
import org.jacoco.report.internal.html.HTMLGroupVisitor;
import org.jacoco.report.internal.html.IHTMLReportContext;
import org.jacoco.report.internal.html.ILinkable;
import org.jacoco.report.internal.html.index.ElementIndex;
import org.jacoco.report.internal.html.index.IIndexUpdate;
import org.jacoco.report.internal.html.page.BundlePage;
import org.jacoco.report.internal.html.page.ReportPage;
import org.jacoco.report.internal.html.page.SessionsPage;
import org.jacoco.report.internal.html.resources.Resources;
import org.jacoco.report.internal.html.resources.Styles;
import org.jacoco.report.internal.html.table.BarColumn;
import org.jacoco.report.internal.html.table.CounterColumn;
import org.jacoco.report.internal.html.table.LabelColumn;
import org.jacoco.report.internal.html.table.PercentageColumn;
import org.jacoco.report.internal.html.table.Table;
import org.jacoco.report.util.LineInfo;
import org.jacoco.report.util.MethodInfo;
import org.slf4j.LoggerFactory;

/**
 * Formatter for coverage reports in multiple HTML pages.
 */
public class HTMLFormatter implements IHTMLReportContext {

    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(HTMLFormatter.class);

    private ILanguageNames languageNames = new JavaNames();

    private Locale locale = Locale.getDefault();

    private String footerText = "";

    private String outputEncoding = "UTF-8";

    private Resources resources;

    private ElementIndex index;

    private SessionsPage sessionsPage;

    private Table table;

    /**
     * New instance with default settings.
     */
    public HTMLFormatter() {
    }

    /**
     * Sets the implementation for language name display. Java language names
     * are defined by default.
     *
     * @param languageNames converter for language specific names
     */
    public void setLanguageNames(final ILanguageNames languageNames) {
        this.languageNames = languageNames;
    }

    /**
     * Sets the locale used for report rendering. The current default locale is
     * used by default.
     *
     * @param locale locale used for report rendering
     */
    public void setLocale(final Locale locale) {
        this.locale = locale;
    }

    /**
     * Sets the optional text that should be included in every footer page.
     *
     * @param footerText footer text
     */
    public void setFooterText(final String footerText) {
        this.footerText = footerText;
    }

    /**
     * Sets the encoding used for generated HTML pages. Default is UTF-8.
     *
     * @param outputEncoding HTML output encoding
     */
    public void setOutputEncoding(final String outputEncoding) {
        this.outputEncoding = outputEncoding;
    }

    // === IHTMLReportContext ===

    public ILanguageNames getLanguageNames() {
        return languageNames;
    }

    public Resources getResources() {
        return resources;
    }

    public Table getTable() {
        if (table == null) {
            table = createTable();
        }
        return table;
    }

    private Table createTable() {
        final Table t = new Table();
        t.add("Element", null, new LabelColumn(), false);
        t.add("Missed Instructions", Styles.BAR, new BarColumn(CounterEntity.INSTRUCTION,
                locale), true);
        t.add("Cov.", Styles.CTR2,
                new PercentageColumn(CounterEntity.INSTRUCTION, locale), false);
        t.add("Missed Branches", Styles.BAR, new BarColumn(CounterEntity.BRANCH, locale),
                false);
        t.add("Cov.", Styles.CTR2, new PercentageColumn(CounterEntity.BRANCH, locale),
                false);
        addMissedTotalColumns(t, "Cxty", CounterEntity.COMPLEXITY);
        addMissedTotalColumns(t, "Lines", CounterEntity.LINE);
        addMissedTotalColumns(t, "Methods", CounterEntity.METHOD);
        addMissedTotalColumns(t, "Classes", CounterEntity.CLASS);
        return t;
    }

    private void addMissedTotalColumns(final Table table, final String label,
            final CounterEntity entity) {
        table.add("Missed", Styles.CTR1,
                CounterColumn.newMissed(entity, locale), false);
        table.add(label, Styles.CTR2, CounterColumn.newTotal(entity, locale),
                false);
    }

    public String getFooterText() {
        return footerText;
    }

    public ILinkable getSessionsPage() {
        return sessionsPage;
    }

    public String getOutputEncoding() {
        return outputEncoding;
    }

    public IIndexUpdate getIndexUpdate() {
        return index;
    }

    public Locale getLocale() {
        return locale;
    }

    /**
     * Creates a new visitor to write a report to the given output.
     *
     * @param output output to write the report to
     * @return visitor to emit the report data to
     * @throws IOException in case of problems with the output stream
     */
    public IReportVisitor createVisitor(final IMultiReportOutput output, final JSONObject object)
            throws IOException {
        final ReportOutputFolder root = new ReportOutputFolder(output);
        resources = new Resources(root);
        resources.copyResources();
        index = new ElementIndex(root);
        return new IReportVisitor() {

            private List<SessionInfo> sessionInfos;

            private Collection<ExecutionData> executionData;

            private HTMLGroupVisitor groupHandler;

            public void visitInfo(final List<SessionInfo> sessionInfos,
                    final Collection<ExecutionData> executionData)
                    throws IOException {
                this.sessionInfos = sessionInfos;
                this.executionData = executionData;
            }

            /**
             * 修改返回值状态
             * void 改成 Map<String, Object> 会涉及很多给类都需要修改
             * AbstractGroupVisitor、XMLFormatter、RulesChecker、MultiReportVisitor、CSVGroupHandler、IReportGroupVisitor都有修改
             * @param bundle
             *            a bundle to include in the report
             * @param locator
             *            source locator for this bundle
             * @return
             * @throws IOException
             */
            public Map<String, Object> visitBundle(final IBundleCoverage bundle,
                    final ISourceFileLocator locator) throws IOException {
                final BundlePage page = new BundlePage(bundle, null, locator,
                        root, HTMLFormatter.this);
                createSessionsPage(page);

                /*********获取代码覆盖率的关联关系**begin*******/
                List<MethodInfo> methodInfoList = page.renderMethodInfoList();
                setFirstAndLastLine(methodInfoList);
                List<LineInfo> lineInfoList = page.render();
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("methodInfoList", methodInfoList);
                map.put("lineInfoList", lineInfoList);
                return map;
                /*********获取代码覆盖率的关联关系**end*******/

            }

//			/**
//			 * 直接保存到数据库中
//			 * @param name
//			 *            name of the group
//			 * @return
//			 * @throws IOException
//			 */
//			public void visitBundle(final IBundleCoverage bundle,
//					final ISourceFileLocator locator) throws IOException {
//				final BundlePage page = new BundlePage(bundle, null, locator,
//						root, HTMLFormatter.this);
//				createSessionsPage(page);
//
//				/*********获取代码覆盖率的关联关系**begin*******/
//				List<MethodInfo> methodInfoList = page.renderMethodInfoList();
//				List<LineInfo> lineInfoList = page.render();
//
//				if(null != lineInfoList && lineInfoList.size() > 0){
//					for(LineInfo lineInfo : lineInfoList) {
//						if(lineInfo.getStatus() != -1) {
//							JDBCUtils.inserLineInfo(lineInfo);
//						}
//					}
//				}
//
//				if(null != methodInfoList && methodInfoList.size() > 0) {
//					for( MethodInfo methodInfo : methodInfoList) {
//						JDBCUtils.insertMethodInfo(methodInfo);
//					}
//				}
//				/*********获取代码覆盖率的关联关系**end*******/
//
////				page.render();
//			}

            public IReportGroupVisitor visitGroup(final String name)
                    throws IOException {
                groupHandler = new HTMLGroupVisitor(null, root,
                        HTMLFormatter.this, name);
                createSessionsPage(groupHandler.getPage());
                return groupHandler;

            }

            private void createSessionsPage(final ReportPage rootpage) {
                sessionsPage = new SessionsPage(sessionInfos, executionData,
                        index, rootpage, root, HTMLFormatter.this);
            }

            public void visitEnd() throws IOException {
                if (groupHandler != null) {
                    groupHandler.visitEnd();
                }
                sessionsPage.render();
                output.close();
            }
        };
    }

    public static void setFirstAndLastLine(List<org.jacoco.report.util.MethodInfo> methodInfoList) {
        if (null == methodInfoList || methodInfoList.size() == 0 || null == CoverageBuilder.classInfos) {
            return;
        }
        for (ClassInfo classInfo : CoverageBuilder.classInfos) {
            String sourceFileName = classInfo.getPackages() + "." + classInfo.getClassName();
            for (org.jacoco.report.util.MethodInfo methodInfo : methodInfoList) {
                String sourceFileNameTemp = methodInfo.getPackageName() + "." + methodInfo.getClassName();
                if (!sourceFileName.equals(sourceFileNameTemp)) {
                    continue;
                }
                for (org.jacoco.core.internal.diff.MethodInfo methodInfoTemp : classInfo.getMethodInfos()) {
                    if (!methodInfo.getMethodName().replaceAll(" ", "")
                            .equals(methodInfoTemp.getMethodName() + methodInfoTemp.getParameterStr())) {
                        continue;
                    }
                    methodInfoTemp.setFirstLine(methodInfo.getFirstLine());
                    methodInfoTemp.setLastLine(methodInfo.getLastLine());
                    break;
                }
            }
        }
    }

    public static String getMethodName(String methodName, String params) {
        if (null == methodName) {
            return "";
        }
        if (null == params) {
            return methodName + "()";
        }
        if (params.startsWith("[")) {
            params = params.replaceFirst("\\[", "");
        }
        if (params.endsWith("]")) {
            params = params.substring(0, params.lastIndexOf("]"));
        }
        Pattern pattern8 = Pattern.compile("@RequestParam\\((.*?)\\)");
        Matcher matcher8 = pattern8.matcher(params);
        while (matcher8.find()) {
            params = params.replace(matcher8.group(), "");
        }
        Pattern pattern3 = Pattern.compile("LinkedHashMap<(.*?)>>");
        Matcher matcher3 = pattern3.matcher(params);
        while (matcher3.find()) {
            params = params.replace(matcher3.group(), "LinkedHashMap");
        }
        Pattern pattern2 = Pattern.compile("LinkedHashMap<(.*?)>");
        Matcher matcher2 = pattern2.matcher(params);
        while (matcher2.find()) {
            params = params.replace(matcher2.group(), "LinkedHashMap");
        }
        Pattern pattern12 = Pattern.compile("<(.*?)>");
        Matcher matcher12 = pattern12.matcher(params);
        while (matcher12.find()) {
            params = params.replace(matcher12.group(), "");
        }
        Pattern pattern10 = Pattern.compile("Set<(.*?)>");
        Matcher matcher10 = pattern10.matcher(params);
        while (matcher10.find()) {
            params = params.replace(matcher10.group(), "Set");
        }
        if (params.contains("<?>")) {
            params = params.replace("<?>", "");
        }
        Pattern pattern7 = Pattern.compile("Map<(.*?)>>>");
        Matcher matcher7 = pattern7.matcher(params);
        while (matcher7.find()) {
            params = params.replace(matcher7.group(), "Map");
        }
        Pattern pattern4 = Pattern.compile("Map<(.*?)>>");
        Matcher matcher4 = pattern4.matcher(params);
        while (matcher4.find()) {
            params = params.replace(matcher4.group(), "Map");
        }
        Pattern pattern6 = Pattern.compile("Map.Entry<(.*?)>");
        Matcher matcher6 = pattern6.matcher(params);
        while (matcher6.find()) {
            params = params.replace(matcher6.group(), "Entry");
        }
        Pattern pattern1 = Pattern.compile("Map<(.*?)>");
        Matcher matcher1 = pattern1.matcher(params);
        while (matcher1.find()) {
            params = params.replace(matcher1.group(), "Map");
        }
        Pattern pattern13 = Pattern.compile("List<(.*?)>>>");
        Matcher matcher13 = pattern13.matcher(params);
        while (matcher13.find()) {
            params = params.replace(matcher13.group(), "List");
        }
        Pattern pattern14 = Pattern.compile("List<(.*?)>>");
        Matcher matcher14 = pattern14.matcher(params);
        while (matcher14.find()) {
            params = params.replace(matcher14.group(), "List");
        }
        Pattern pattern15 = Pattern.compile("List<(.*?)>");
        Matcher matcher15 = pattern15.matcher(params);
        while (matcher15.find()) {
            params = params.replace(matcher15.group(), "List");
        }
        Pattern pattern = Pattern.compile("(?<=\\()(.+?)(?=\\))");
        Matcher matcher = pattern.matcher(params);
        while (matcher.find()) {
            params = params.replace(matcher.group(), "");
        }
        if (params.contains(">")) {
            params = params.replace(">", "");
        }

        final StringBuilder sb = new StringBuilder();
        sb.append("(");
        final String[] paramsArr = params.split(",");
        boolean comma = false;
        for (final String str : paramsArr) {
            try {
                if (comma) {
                    sb.append(", ");
                } else {
                    comma = true;
                }
                String newStr = str.trim();
                String[] tempArr = newStr.split("@");
                if (null != tempArr) {
                    for (int i = 0; i < tempArr.length; i++) {
                        if (newStr.trim().startsWith("@")) {
                            newStr = newStr.trim().substring(newStr.trim().indexOf(" "),
                                    newStr.trim().length());
                        }
                    }
                }
                String[] tempStr = newStr.trim().split(" ");
                for (String s : tempStr) {
                    if (s.equals("final")) {
                        continue;
                    }
                    newStr = s.trim();
                    break;
                }
                if (newStr.contains("...")) {
                    newStr = newStr.replace("...", "[]");
                }
                if (newStr.contains("<")) {
                    newStr = newStr.split("<")[0];
                }
                if (newStr.equals("T")) {
                    newStr = newStr.replace(newStr, "Object");
                }
                if (newStr.contains(".")) {
                    String[] tempArr1 = newStr.trim().split("\\.");
                    newStr = tempArr1[tempArr1.length - 1];
                }
                sb.append(newStr);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        sb.append(')');
        return (methodName + sb).replaceAll(" ", "");
    }
}
