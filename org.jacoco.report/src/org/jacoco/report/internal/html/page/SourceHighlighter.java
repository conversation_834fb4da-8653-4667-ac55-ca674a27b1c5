/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R. Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.report.internal.html.page;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import org.jacoco.core.analysis.CoverageBuilder;
import org.jacoco.core.analysis.ICounter;
import org.jacoco.core.analysis.ILine;
import org.jacoco.core.analysis.ISourceNode;
import org.jacoco.core.internal.analysis.CounterImpl;
import org.jacoco.core.internal.analysis.LineImpl;
import org.jacoco.core.internal.analysis.SourceFileCoverageImpl;
import org.jacoco.core.internal.diff.ClassInfo;
import org.jacoco.core.internal.diff.MethodInfo;
import org.jacoco.report.internal.html.HTMLElement;
import org.jacoco.report.internal.html.resources.Styles;
import org.jacoco.report.util.LineInfo;
import org.slf4j.LoggerFactory;

/**
 * Creates a highlighted output of a source file.
 */
final class SourceHighlighter {

    private static final org.slf4j.Logger logger = LoggerFactory.getLogger(SourceHighlighter.class);

    private final Locale locale;

    private String lang;

    /**
     * Creates a new highlighter with default settings.
     *
     * @param locale locale for tooltip rendering
     */
    public SourceHighlighter(final Locale locale) {
        this.locale = locale;
        lang = "java";
    }

    /**
     * Specifies the source language. This value might be used for syntax
     * highlighting. Default is "java".
     *
     * @param lang source language identifier
     */
    public void setLanguage(final String lang) {
        this.lang = lang;
    }

    /**
     * Highlights the given source file.
     *
     * @param parent   parent HTML element
     * @param source   highlighting information
     * @param contents contents of the source file
     * @throws IOException problems while reading the source file or writing the output
     */
//	public void render(final HTMLElement parent, final ISourceNode source,
//			final Reader contents) throws IOException {
//		final HTMLElement pre = parent.pre(Styles.SOURCE + " lang-" + lang
//				+ " linenums");
//		final BufferedReader lineBuffer = new BufferedReader(contents);
//		String classPath = ((SourceFileCoverageImpl) source).getPackageName() + "." + source.getName().replaceAll(".java","");
//		classPath = classPath.replaceAll("/",".");
//		String line;
//		int nr = 0;
//		while ((line = lineBuffer.readLine()) != null) {
//			nr++;
//			renderCodeLine(pre, line, source.getLine(nr), nr,classPath);
//		}
//	}
    public List<LineInfo> render(final HTMLElement parent, final ISourceNode source,
                                 final Reader contents) throws IOException {
        final HTMLElement pre = parent.pre(Styles.SOURCE + " lang-" + lang
                + " linenums");
        final BufferedReader lineBuffer = new BufferedReader(contents);
        String classPath =
                ((SourceFileCoverageImpl) source).getPackageName() + "." + source.getName().replaceAll(".java", "");
        classPath = classPath.replaceAll("/", ".");
        String line;
        int nr = 0;
        SourceFileCoverageImpl sourceFileCoverage = (SourceFileCoverageImpl) source;
        String packageName = sourceFileCoverage.getPackageName().replaceAll("/", ".");
        String className = sourceFileCoverage.getName().replaceAll(".java", "");
        List<LineInfo> lineInfoList = new ArrayList<LineInfo>();
        while ((line = lineBuffer.readLine()) != null) {
            nr++;
            LineInfo lineInfo = renderCodeLine(pre, line, source.getLine(nr), nr, classPath, packageName, className);
            //如果没有覆盖的不保存
//			if(lineInfo.getStatus() != -1){
            lineInfoList.add(lineInfo);
//			}
        }
        return lineInfoList;
    }

//也是修改后的 可以直接删除
//	private LineInfo renderCodeLine(final HTMLElement pre, final String linesrc,
//									final ILine line, final int lineNr, String classPath, String packagename, String className) throws IOException {
//		highlight(pre, line, lineNr,classPath,packagename,className).text(linesrc);
//		LineInfo lineInfo = highlightNew(pre, line, lineNr,classPath,packagename,className);
//		pre.text("\n");
//		return lineInfo;
//	}


    private LineInfo renderCodeLine(final HTMLElement pre, final String linesrc,
                                    ILine line, final int lineNr, String classPath, String packagename, String className) throws IOException {
        LineInfo lineInfo = null;
        if (CoverageBuilder.classInfos == null || CoverageBuilder.classInfos.isEmpty()) {
            //	全量覆盖
            highlight(pre, line, lineNr).text(linesrc);
            lineInfo = highlightNew(pre, line, lineNr, classPath, packagename, className);

            pre.text("\n");
        } else {
            //	增量覆盖
            boolean existFlag = true;
            for (ClassInfo classInfo : CoverageBuilder.classInfos) {
                String tClassPath = classInfo.getPackages() + "." + classInfo.getClassName();
                if (classPath.equals(tClassPath)) {
                    //	新增的类
                    if ("ADD".equalsIgnoreCase(classInfo.getType())) {
                        highlight(pre, line, lineNr).text("+ " + linesrc);
                        lineInfo = highlightNew(pre, line, lineNr, classPath, packagename, className);

                        pre.text("\n");
                    } else {
                        if (line.getStatus() != 0 && line.getStatus() != 2) {
                            line = getNewLine(line, classInfo, lineNr);
                        }
                        //	修改的类
                        boolean flag = false;
                        List<int[]> addLines = classInfo.getAddLines();
                        for (int[] ints : addLines) {
                            if (ints[0] <= lineNr && lineNr <= ints[1]) {
                                flag = true;
                                break;
                            }
                        }
                        if (flag) {
                            highlight(pre, line, lineNr).text("+ " + linesrc);
                            lineInfo = highlightNew(pre, line, lineNr, classPath, packagename, className);
                            pre.text("\n");
                        } else {
                            highlight(pre, line, lineNr).text(" " + linesrc);
                            lineInfo = highlightNew(pre, line, lineNr, classPath, packagename, className);
                            pre.text("\n");
                        }
                    }
                    existFlag = false;
                    break;
                }
            }
            if (existFlag) {
                highlight(pre, line, lineNr).text(" " + linesrc);
                lineInfo = highlightNew(pre, line, lineNr, classPath, packagename, className);
                pre.text("\n");
            }
        }
        return lineInfo;
    }

    /**
     * 原来没有的方法 后增加的 为了获取关联关系保存数据
     *
     * @param pre
     * @param line
     * @param lineNr
     * @param classPath
     * @param packagename
     * @param className
     * @return
     */
    private LineInfo highlightNew(HTMLElement pre, ILine line, int lineNr, String classPath, String packagename,
                                  String className) {
        LineInfo lineInfo = new LineInfo();
        lineInfo.setClassName(className);
        lineInfo.setPackageName(packagename);
        lineInfo.setLine(lineNr);

        switch (line.getStatus()) {
            case ICounter.NOT_COVERED:
                lineInfo.setStatus(ICounter.NOT_COVERED);///1 未涵盖 红色
                lineInfo.setLine(lineNr);
                break;
            case ICounter.FULLY_COVERED://2 完全覆盖 绿色
                lineInfo.setStatus(ICounter.FULLY_COVERED);
                lineInfo.setLine(lineNr);
                break;
            case ICounter.PARTLY_COVERED://3 部分覆盖 黄色
                lineInfo.setStatus(ICounter.PARTLY_COVERED);
                lineInfo.setLine(lineNr);
                break;
            default:
                lineInfo.setStatus(-1); // 白色
                lineInfo.setLine(lineNr);
        }
        return lineInfo;

    }


//	HTMLElement highlight(final HTMLElement pre, final ILine line,
//			final int lineNr) throws IOException {
//		final String style;
//		switch (line.getStatus()) {
//		case ICounter.NOT_COVERED:
//			style = Styles.NOT_COVERED;
//			break;
//		case ICounter.FULLY_COVERED:
//			style = Styles.FULLY_COVERED;
//			break;
//		case ICounter.PARTLY_COVERED:
//			style = Styles.PARTLY_COVERED;
//			break;
//		default:
//			return pre;
//		}
//
//		final String lineId = "L" + Integer.toString(lineNr);
//		final ICounter branches = line.getBranchCounter();
//		switch (branches.getStatus()) {
//		case ICounter.NOT_COVERED:
//			return span(pre, lineId, style, Styles.BRANCH_NOT_COVERED,
//					"All %2$d branches missed.", branches);
//		case ICounter.FULLY_COVERED:
//			return span(pre, lineId, style, Styles.BRANCH_FULLY_COVERED,
//					"All %2$d branches covered.", branches);
//		case ICounter.PARTLY_COVERED:
//			return span(pre, lineId, style, Styles.BRANCH_PARTLY_COVERED,
//					"%1$d of %2$d branches missed.", branches);
//		default:
//			return pre.span(style, lineId);
//		}
//	}

    HTMLElement highlight(final HTMLElement pre, final ILine line,
                          final int lineNr) throws IOException {

        final String style;
        switch (line.getStatus()) {
            case ICounter.NOT_COVERED:
                style = Styles.NOT_COVERED;
                break;
            case ICounter.FULLY_COVERED:
//				System.out.println("FULLY_COVERED" + lineNr);
                style = Styles.FULLY_COVERED;
                break;
            case ICounter.PARTLY_COVERED:
//				System.out.println("PARTLY_COVERED" + lineNr);
                style = Styles.PARTLY_COVERED;
                break;
            default:
                return pre;
        }

        final String lineId = "L" + Integer.toString(lineNr);
        final ICounter branches = line.getBranchCounter();
        switch (branches.getStatus()) {
            case ICounter.NOT_COVERED:
                return span(pre, lineId, style, Styles.BRANCH_NOT_COVERED,
                        "All %2$d branches missed.", branches);
            case ICounter.FULLY_COVERED:

                return span(pre, lineId, style, Styles.BRANCH_FULLY_COVERED,
                        "All %2$d branches covered.", branches);
            case ICounter.PARTLY_COVERED:

                return span(pre, lineId, style, Styles.BRANCH_PARTLY_COVERED,
                        "%1$d of %2$d branches missed.", branches);
            default:
                return pre.span(style, lineId);
        }
    }

    private HTMLElement span(final HTMLElement parent, final String id,
                             final String style1, final String style2, final String title,
                             final ICounter branches) throws IOException {
        final HTMLElement span = parent.span(style1 + " " + style2, id);
        final Integer missed = Integer.valueOf(branches.getMissedCount());
        final Integer total = Integer.valueOf(branches.getTotalCount());
        span.attr("title", String.format(locale, title, missed, total));
        return span;
    }

    public static ILine getNewLine(ILine line, ClassInfo classInfo, int lineNr) {
        if (null == classInfo) {
            return line;
        }
        List<MethodInfo> methodInfoList = classInfo.getMethodInfos();
        if (null != methodInfoList && methodInfoList.size() > 0) {
            for (MethodInfo methodInfo : methodInfoList) {
                if (null == methodInfo.getFirstLine() && null == methodInfo
                        .getLastLine()) {
                    continue;
                }
                JSONArray methodInfoReportLineList = methodInfo.getReportLineList();
                if (null != methodInfoReportLineList && methodInfoReportLineList.size() > 0) {
                    for (int i = 0; i < methodInfoReportLineList.size(); i++) {
                        JSONObject methodInfoReportLine = methodInfoReportLineList.getJSONObject(i);
                        if (null == methodInfo.getFirstLine() || null == methodInfo
                                .getLastLine()) {
                            throw new IllegalArgumentException("获取方法行号异常。" +
                                    "类名：" + classInfo.getPackages() + "/" + classInfo.getClassName()
                                    + ",方法名: " + methodInfo.getMethodName()
                                    + ",参数： " + methodInfo.getParameters());
                        }
                        if (null == methodInfoReportLine || lineNr < methodInfo.getFirstLine() || lineNr > methodInfo
                                .getLastLine()) {
                            break;
                        }
                        if (null == methodInfo.getIsCovered() || !methodInfo.getIsCovered()) {
                            return line;
                        }
                        return getLine(methodInfoReportLine);
                    }
                }
            }
        }
        return line;
    }

    public static LineImpl getLine(final JSONObject methodInfoReportLine) {
        final LineImpl line = LineImpl.EMPTY;
        return line.increment(CounterImpl
                        .getInstance(0, methodInfoReportLine.getInteger("mi") + methodInfoReportLine.getInteger("ci")),
                CounterImpl
                        .getInstance(0, methodInfoReportLine.getInteger("mb") + methodInfoReportLine.getInteger("cb")));
    }

}
