<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.build</artifactId>
    <version>0.8.4</version>
    <relativePath>../org.jacoco.build</relativePath>
  </parent>

  <artifactId>org.jacoco.core</artifactId>
  <version>0.8.19</version>

  <name>JaCoCo :: Core</name>
  <description>JaCoCo Core</description>

  <distributionManagement>
    <repository>
      <id>releases</id>
      <name>libs-release</name>
      <url>https://jfrog.dev.ztosys.com/artifactory/libs-release/</url>
    </repository>
    <snapshotRepository>
      <id>snapshots</id>
      <name>libs-snapshot</name>
      <url>https://jfrog.dev.ztosys.com/artifactory/libs-snapshot/</url>
    </snapshotRepository>
  </distributionManagement>

  <dependencies>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm</artifactId>
      <version>7.1</version>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-commons</artifactId>
      <version>7.1</version>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-tree</artifactId>
      <version>7.1</version>
    </dependency>
    <!--java文件编译class-->
    <dependency>
      <groupId>org.eclipse.jdt</groupId>
      <artifactId>org.eclipse.jdt.core</artifactId>
      <version>3.16.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.osgi.service</groupId>
          <artifactId>org.osgi.service.prefs</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.core.runtime</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.core.resources</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.equinox.preferences</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.platform</groupId>
      <artifactId>org.eclipse.core.runtime</artifactId>
      <version>3.18.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.osgi.service</groupId>
          <artifactId>org.osgi.service.prefs</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.equinox.common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.core.jobs</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.core.contenttype</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.equinox.preferences</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.platform</groupId>
      <artifactId>org.eclipse.equinox.common</artifactId>
      <version>3.14.100</version>
      <exclusions>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.equinox.preferences</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.platform</groupId>
      <artifactId>org.eclipse.core.resources</artifactId>
      <version>3.14.0</version>
      <exclusions>
        <exclusion>
          <groupId>org.osgi.service</groupId>
          <artifactId>org.osgi.service.prefs</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.equinox.preferences</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.platform</groupId>
      <artifactId>org.eclipse.core.jobs</artifactId>
      <version>3.10.1100</version>
      <exclusions>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.equinox.preferences</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.eclipse.platform</groupId>
      <artifactId>org.eclipse.core.contenttype</artifactId>
      <version>3.7.1000</version>
      <exclusions>
        <exclusion>
          <groupId>org.osgi.service</groupId>
          <artifactId>org.osgi.service.prefs</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.eclipse.platform</groupId>
          <artifactId>org.eclipse.equinox.preferences</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.service.prefs</artifactId>
      <version>1.1.2</version>
    </dependency>
    <dependency>
      <groupId>org.eclipse.platform</groupId>
      <artifactId>org.eclipse.equinox.preferences</artifactId>
      <version>3.9.100</version>
    </dependency>
    <!--git操作-->
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit</artifactId>
      <version>5.5.0.201909110433-r</version>
    </dependency>
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.32</version>
    </dependency>
    <dependency>
      <groupId>com.github.cretz.kastree</groupId>
      <artifactId>kastree-ast-psi</artifactId>
      <version>0.4.0</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.20</version>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>

    <plugins>
      <plugin>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <executions>
          <execution>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <source>7</source>
          <target>7</target>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
