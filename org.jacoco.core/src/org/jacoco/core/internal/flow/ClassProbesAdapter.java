/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R<PERSON> Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.core.internal.flow;

import lombok.extern.slf4j.Slf4j;
import org.jacoco.core.analysis.CoverageBuilder;
import org.jacoco.core.internal.diff.ClassInfo;
import org.jacoco.core.internal.diff.MethodInfo;
import org.jacoco.core.internal.instr.InstrSupport;
import org.jacoco.core.tools.ILanguageNames;
import org.jacoco.core.tools.JavaNames;
import org.objectweb.asm.ClassVisitor;
import org.objectweb.asm.MethodVisitor;
import org.objectweb.asm.commons.AnalyzerAdapter;

import java.util.List;

/**
 * A {@link org.objectweb.asm.ClassVisitor} that calculates probes for every
 * method.
 */
@Slf4j
public class ClassProbesAdapter extends ClassVisitor implements
        IProbeIdGenerator {

    private static final MethodProbesVisitor EMPTY_METHOD_PROBES_VISITOR = new MethodProbesVisitor() {
    };

    private final ClassProbesVisitor cv;

    private final boolean trackFrames;

    private int counter = 0;

    private String name;

    private ILanguageNames javaNames = new JavaNames();

    /**
     * Creates a new adapter that delegates to the given visitor.
     *
     * @param cv          instance to delegate to
     * @param trackFrames if <code>true</code> stackmap frames are tracked and provided
     */
    public ClassProbesAdapter(final ClassProbesVisitor cv,
                              final boolean trackFrames) {
        super(InstrSupport.ASM_API_VERSION, cv);
        this.cv = cv;
        this.trackFrames = trackFrames;
    }

    @Override
    public void visit(final int version, final int access, final String name,
                      final String signature, final String superName,
                      final String[] interfaces) {
        this.name = name;
        super.visit(version, access, name, signature, superName, interfaces);
    }

    @Override
    public final MethodVisitor visitMethod(final int access, final String name,
                                           final String desc, final String signature, final String[] exceptions) {
        final MethodProbesVisitor methodProbes;

        final MethodProbesVisitor mv = cv.visitMethod(access, name, desc,
                signature, exceptions, CoverageBuilder.reportJson, CoverageBuilder.classInfos);
        //	增量计算覆盖率
        if (mv != null && isContainsMethod(name, desc, CoverageBuilder.classInfos)) {
            methodProbes = mv;
        } else {
            // We need to visit the method in any case, otherwise probe ids
            // are not reproducible
            methodProbes = EMPTY_METHOD_PROBES_VISITOR;
        }
        return new MethodSanitizer(null, access, name, desc, signature,
                exceptions) {

            @Override
            public void visitEnd() {
                super.visitEnd();
                LabelFlowAnalyzer.markLabels(this);
                final MethodProbesAdapter probesAdapter = new MethodProbesAdapter(
                        methodProbes, ClassProbesAdapter.this);
                if (trackFrames) {
                    final AnalyzerAdapter analyzer = new AnalyzerAdapter(
                            ClassProbesAdapter.this.name, access, name, desc,
                            probesAdapter);
                    probesAdapter.setAnalyzer(analyzer);
                    methodProbes.accept(this, analyzer);
                } else {
                    methodProbes.accept(this, probesAdapter);
                }
            }
        };
    }

    @Override
    public void visitEnd() {
        cv.visitTotalProbeCount(counter);
        super.visitEnd();
    }

    // === IProbeIdGenerator ===

    public int nextId() {
        return counter++;
    }

    private boolean isContainsMethod(String currentMethod, String currentDesc, List<ClassInfo> classInfos) {
        if (classInfos == null || classInfos.isEmpty()) {
            return true;
        }
        String currentClassName = name.replaceAll("/", ".");
        for (ClassInfo classInfo : classInfos) {
            String className = classInfo.getPackages() + "." + classInfo.getClassName();
            if (currentClassName.equals(className)) {
                for (MethodInfo methodInfo : classInfo.getMethodInfos()) {
                    if (isTheSameMethod(methodInfo, currentMethod, currentDesc)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean isTheSameMethod(MethodInfo methodInfo, String methodName, String desc) {
        String method1 = methodInfo.getMethodName() + methodInfo.getParameterStr();
//        String method2 = name + handlerParam(desc);
        String method2 = javaNames.getMethodName(name, methodName, desc, null);
        if (method1.equals(method2)) {
            return true;
        }
        return false;
    }

    private static String handlerParam(String desc) {
        if (null == desc || "".equals(desc)) {
            return "()";
        }
        if (desc.startsWith("(")) {
            int angleBracketIndex = desc.indexOf(")");
            desc = desc.substring(1, angleBracketIndex);
        }
        if (null == desc || "".equals(desc)) {
            return "()";
        }
        boolean comma = false;
        StringBuilder sb = new StringBuilder();
        sb.append("(");
        String[] params = desc.split(";");
        for (String param : params) {
            if (comma) {
                sb.append(",");
            } else {
                comma = true;
            }
            typeSignature2Java(param, sb);
        }
        sb.append(")");
        return sb.toString();
    }

    /**
     * JVM类型转换
     *
     * @return
     */
    private static void typeSignature2Java(String param, StringBuilder sb) {
        if (param.startsWith("Z")) {
            sb.append("boolean");
            param = param.replaceFirst("Z", "");
        } else if (param.startsWith("B")) {
            sb.append("byte");
            param = param.replaceFirst("B", "");
        } else if (param.startsWith("C")) {
            sb.append("char");
            param = param.replaceFirst("C", "");
        } else if (param.startsWith("S")) {
            sb.append("short");
            param = param.replaceFirst("S", "");
        } else if (param.startsWith("I")) {
            sb.append("int");
            param = param.replaceFirst("I", "");
        } else if (param.startsWith("J")) {
            sb.append("long");
            param = param.replaceFirst("J", "");
        } else if (param.startsWith("F")) {
            sb.append("float");
            param = param.replaceFirst("F", "");
        } else if (param.startsWith("D")) {
            sb.append("double");
            param = param.replaceFirst("D", "");
        } else if (param.startsWith("[Z")) {
            sb.append("boolean[]");
            param = param.replaceFirst("\\[Z", "");
        } else if (param.startsWith("[B")) {
            sb.append("byte[]");
            param = param.replaceFirst("\\[B", "");
        } else if (param.startsWith("[C")) {
            sb.append("char[]");
            param = param.replaceFirst("\\[C", "");
        } else if (param.startsWith("[S")) {
            sb.append("short[]");
            param = param.replaceFirst("\\[S", "");
        } else if (param.startsWith("[I")) {
            sb.append("int[]");
            param = param.replaceFirst("\\[I", "");
        } else if (param.startsWith("[J")) {
            sb.append("long[]");
            param = param.replaceFirst("\\[J", "");
        } else if (param.startsWith("[F")) {
            sb.append("float[]");
            param = param.replaceFirst("\\[F", "");
        } else if (param.startsWith("[D")) {
            sb.append("double[]");
            param = param.replaceFirst("\\[D", "");
        }
        if (param.startsWith("L")) {
            int lastIndex = param.lastIndexOf("/");
            param = param.substring(lastIndex + 1);
            if (param.contains("$")) {
                int last$ = param.lastIndexOf("$");
                param = param.substring(last$ + 1);
            }
            if (!sb.toString().equals("(") && !sb.toString().endsWith(",")) {
                sb.append(",");
            }
        }
        if (param.startsWith("[L")) {
            int lastIndex = param.lastIndexOf("/");
            param = param.substring(lastIndex + 1);
            if (param.contains("$")) {
                int last$ = param.lastIndexOf("$");
                param = param.substring(last$ + 1) + "[]";
            } else {
                param = param + "[]";
            }
            if (!sb.toString().equals("(") && !sb.toString().endsWith(",")) {
                sb.append(",");
            }
        }
        sb.append(param);
    }
}
