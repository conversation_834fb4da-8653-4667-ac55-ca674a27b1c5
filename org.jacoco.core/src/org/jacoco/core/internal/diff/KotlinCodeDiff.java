/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R. Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.core.internal.diff;

import kastree.ast.Node;
import org.eclipse.jdt.core.dom.MethodDeclaration;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.diff.*;
import org.eclipse.jgit.lib.ObjectId;
import org.eclipse.jgit.lib.ObjectReader;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.patch.FileHeader;
import org.eclipse.jgit.treewalk.CanonicalTreeParser;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 代码版本比较
 */
public class KotlinCodeDiff {

    /**
     * 不同分支版本对比
     *
     * @param gitPath
     * @param oldBranchName
     * @param oldCommitId
     * @param newBranchName
     * @param newCommitId
     * @return
     */
    public static List<ClassInfo> diffBranchCommitIdToBranchCommitId(String gitPath,
                                                                     String oldBranchName, String oldCommitId,
                                                                     String newBranchName, String newCommitId) {
        try {
            //  获取本地分支
            GitAdapter gitAdapter = new GitAdapter(gitPath);
            Git git = gitAdapter.getGit();
            Repository repository = gitAdapter.getRepository();
            //  更新本地分支
            Ref oldBranchRef = repository.exactRef("refs/heads/" + oldBranchName);
            gitAdapter.checkOutAndPull(oldBranchRef, oldBranchName);
            if (!oldBranchName.equals(newBranchName)) {
                Ref newBranchRef = repository.exactRef("refs/heads/" + newBranchName);
                gitAdapter.checkOutAndPull(newBranchRef, newBranchName);
            }
            //  获取分支信息
            ObjectId preVersionId = repository.resolve(oldCommitId + "^{tree}");
            ObjectId versionId = repository.resolve(newCommitId + "^{tree}");

            ObjectReader reader = repository.newObjectReader();
            CanonicalTreeParser oldTreeParser = new CanonicalTreeParser();
            oldTreeParser.reset(reader, preVersionId);
            CanonicalTreeParser newTreeParser = new CanonicalTreeParser();
            newTreeParser.reset(reader, versionId);

            //  对比差异
            List<DiffEntry> diff = git.diff().setOldTree(oldTreeParser).setNewTree(newTreeParser)
                    .setShowNameAndStatusOnly(true).call();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            DiffFormatter df = new DiffFormatter(out);
            //设置比较器为忽略空白字符对比（Ignores all whitespace）
            df.setDiffComparator(RawTextComparator.WS_IGNORE_ALL);
            df.setRepository(git.getRepository());

            return batchPrepareDiffMethod(gitAdapter, oldCommitId, newCommitId, df, diff);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<>();
    }

    /**
     * 多线程执行对比
     *
     * @param gitAdapter
     * @param oldCommitId
     * @param newCommitId
     * @param df
     * @param diffs
     * @return
     */
    private static List<ClassInfo> batchPrepareDiffMethod(final GitAdapter gitAdapter,
                                                          final String oldCommitId, final String newCommitId,
                                                          final DiffFormatter df, List<DiffEntry> diffs) {
        int threadSize = 100;
        int dataSize = diffs.size();
        int threadNum = dataSize / threadSize + 1;
        boolean special = dataSize % threadSize == 0;
        ExecutorService executorService = Executors.newFixedThreadPool(threadNum);

        List<Callable<List<ClassInfo>>> tasks = new ArrayList<>();
        Callable<List<ClassInfo>> task;
        List<DiffEntry> cutList;
        //  分解每条线程的数据
        for (int i = 0; i < threadNum; i++) {
            if (i == threadNum - 1) {
                if (special) {
                    break;
                }
                cutList = diffs.subList(threadSize * i, dataSize);
            } else {
                cutList = diffs.subList(threadSize * i, threadSize * (i + 1));
            }
            final List<DiffEntry> diffEntryList = cutList;
            task = new Callable<List<ClassInfo>>() {
                @Override
                public List<ClassInfo> call() throws Exception {
                    List<ClassInfo> allList = new ArrayList<>();
                    for (DiffEntry diffEntry : diffEntryList) {
                        ClassInfo classInfo = prepareDiffMethod(gitAdapter, oldCommitId, newCommitId, df, diffEntry);
                        if (classInfo != null) {
                            allList.add(classInfo);
                        }
                    }
                    return allList;
                }
            };
            // 这里提交的任务容器列表和返回的Future列表存在顺序对应的关系
            tasks.add(task);
        }
        List<ClassInfo> allClassInfoList = new ArrayList<>();
        try {
            List<Future<List<ClassInfo>>> results = executorService.invokeAll(tasks);
            //结果汇总
            for (Future<List<ClassInfo>> future : results) {
                allClassInfoList.addAll(future.get());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭线程池
            executorService.shutdown();
        }
        return allClassInfoList;
    }

    /**
     * 单个差异文件对比
     *
     * @param gitAdapter
     * @param oldCommitId
     * @param newCommitId
     * @param df
     * @param diffEntry
     * @return
     */
    private synchronized static ClassInfo prepareDiffMethod(GitAdapter gitAdapter,
                                                            String oldCommitId, String newCommitId,
                                                            DiffFormatter df, DiffEntry diffEntry) {
        try {
            String newJavaPath = diffEntry.getNewPath();
            //  排除测试类
            if (newJavaPath.contains("/src/test/java/")) {
                return null;
            }
            //  删除类型不记录
            if (diffEntry.getChangeType() == DiffEntry.ChangeType.DELETE) {
                return null;
            }
            if (newJavaPath.endsWith(".kt")) {
                return prepareDiffMethodForKotlin(gitAdapter, oldCommitId, newCommitId, df, diffEntry);
            }
            if (newJavaPath.endsWith(".java")) {
                return prepareDiffMethodForJava(gitAdapter, oldCommitId, newCommitId, df, diffEntry);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    private synchronized static ClassInfo prepareDiffMethodForKotlin(GitAdapter gitAdapter,
                                                                     String oldCommitId, String newCommitId,
                                                                     DiffFormatter df, DiffEntry diffEntry) {
        List<MethodInfo> methodInfoList = new ArrayList<>();
        try {
            String newKotlinPath = diffEntry.getNewPath();
            String newClassContent = gitAdapter.getTagRevisionSpecificFileContent(newCommitId, newKotlinPath);
            KotlinASTGenerator newAstGenerator = new KotlinASTGenerator(newClassContent, newKotlinPath);
            /*  新增类型   */
            if (diffEntry.getChangeType() == DiffEntry.ChangeType.ADD) {
                return newAstGenerator.getClassInfo();
            }
            /*  修改类型  */
            //  获取文件差异位置，从而统计差异的行数，如增加行数，减少行数
            FileHeader fileHeader = df.toFileHeader(diffEntry);
            List<int[]> addLines = new ArrayList<>();
            List<int[]> delLines = new ArrayList<>();
            EditList editList = fileHeader.toEditList();
            for (Edit edit : editList) {
                if (edit.getLengthA() > 0) {
                    delLines.add(new int[]{edit.getBeginA(), edit.getEndA()});
                }
                if (edit.getLengthB() > 0) {
                    addLines.add(new int[]{edit.getBeginB(), edit.getEndB()});
                }
            }
            String oldKotlinPath = diffEntry.getOldPath();
            String oldClassContent = gitAdapter.getTagRevisionSpecificFileContent(oldCommitId, oldKotlinPath);
            KotlinASTGenerator oldAstGenerator = new KotlinASTGenerator(oldClassContent, null);
            List<Node.Decl.Func> newMethods = newAstGenerator.getMethods();
            List<Node.Decl.Func> oldMethods = oldAstGenerator.getMethods();

            Map<String, Node.Decl.Func> oldMethodsMap = new HashMap<>();
            for (int i = 0; i < oldMethods.size(); i++) {
                oldMethodsMap
                        .put(oldMethods.get(i).getName() + oldMethods.get(i).getParams(), oldMethods.get(i));
            }
            for (final Node.Decl.Func method : newMethods) {
                // 如果方法名是新增的,则直接将方法加入List
                if (!KotlinASTGenerator.isMethodExist(method, oldMethodsMap)) {
                    MethodInfo methodInfo = newAstGenerator.getMethodInfo(method);
                    methodInfoList.add(methodInfo);
                    continue;
                }
                // 如果两个版本都有这个方法,则根据MD5判断方法是否一致
                if (!KotlinASTGenerator.isMethodTheSame(method,
                        oldMethodsMap.get(method.getName() + method.getParams()))) {
                    MethodInfo methodInfo = newAstGenerator.getMethodInfo(method);
                    methodInfoList.add(methodInfo);
                }
            }
            return newAstGenerator.getClassInfo(methodInfoList, addLines, delLines);

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private synchronized static ClassInfo prepareDiffMethodForJava(GitAdapter gitAdapter,
                                                                   String oldCommitId, String newCommitId,
                                                                   DiffFormatter df, DiffEntry diffEntry) {
        List<MethodInfo> methodInfoList = new ArrayList<>();
        try {
            String newJavaPath = diffEntry.getNewPath();
            String newClassContent = gitAdapter.getTagRevisionSpecificFileContent(newCommitId, newJavaPath);
            ASTGenerator newAstGenerator = new ASTGenerator(newClassContent);
            /*  新增类型   */
            if (diffEntry.getChangeType() == DiffEntry.ChangeType.ADD) {
                return newAstGenerator.getClassInfo();
            }
            /*  修改类型  */
            //  获取文件差异位置，从而统计差异的行数，如增加行数，减少行数
            FileHeader fileHeader = df.toFileHeader(diffEntry);
            List<int[]> addLines = new ArrayList<>();
            List<int[]> delLines = new ArrayList<>();
            EditList editList = fileHeader.toEditList();
            for (Edit edit : editList) {
                if (edit.getLengthA() > 0) {
                    delLines.add(new int[]{edit.getBeginA(), edit.getEndA()});
                }
                if (edit.getLengthB() > 0) {
                    addLines.add(new int[]{edit.getBeginB(), edit.getEndB()});
                }
            }
            String oldJavaPath = diffEntry.getOldPath();
            String oldClassContent = gitAdapter.getTagRevisionSpecificFileContent(oldCommitId, oldJavaPath);
            ASTGenerator oldAstGenerator = new ASTGenerator(oldClassContent);
            MethodDeclaration[] newMethods = newAstGenerator.getMethods();
            MethodDeclaration[] oldMethods = oldAstGenerator.getMethods();

            Map<String, MethodDeclaration> oldMethodsMap = new HashMap<>();
            for (int i = 0; i < oldMethods.length; i++) {
                oldMethodsMap
                        .put(oldMethods[i].getName().toString() + oldMethods[i].parameters().toString(), oldMethods[i]);
            }
            for (final MethodDeclaration method : newMethods) {
                // 如果方法名是新增的,则直接将方法加入List
                if (!ASTGenerator.isMethodExist(method, oldMethodsMap)) {
                    MethodInfo methodInfo = getJavaMethodInfo(method);
                    methodInfoList.add(methodInfo);
                    continue;
                }
                // 如果两个版本都有这个方法,则根据MD5判断方法是否一致
                if (!ASTGenerator.isMethodTheSame(method,
                        oldMethodsMap.get(method.getName().toString() + method.parameters().toString()))) {
                    MethodInfo methodInfo = getJavaMethodInfo(method);
                    methodInfoList.add(methodInfo);
                }
            }
            return newAstGenerator.getClassInfo(methodInfoList, addLines, delLines);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static MethodInfo getJavaMethodInfo(MethodDeclaration methodDeclaration) {
        MethodInfo methodInfo = new MethodInfo();
        setJavaMethodInfo(methodInfo, methodDeclaration);
        return methodInfo;
    }

    private static void setJavaMethodInfo(MethodInfo methodInfo, MethodDeclaration methodDeclaration) {
        methodInfo.setMd5(ASTGenerator.MD5Encode(methodDeclaration.toString()));
        methodInfo.setMethodName(methodDeclaration.getName().toString());
        methodInfo.setParameters(setJavaMethodParameters(methodDeclaration.parameters()));
    }

    private static String setJavaMethodParameters(List parameters) {
        StringBuffer sb = new StringBuffer("(");
        if (null != parameters && parameters.size() > 0) {
            for (int i = 0; i < parameters.size(); i++) {
                String[] parameter = parameters.get(i).toString().split(" ");
                sb.append(parameter[parameter.length - 2]);
                if (i < parameters.size() - 1) {
                    sb.append(", ");
                }
            }
        }
        sb.append(")");
        return sb.toString();
    }
}