/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc <PERSON> Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.core.internal.diff;

import com.alibaba.fastjson.JSONArray;

public class MethodInfo {

    /**
     * 方法的md5
     */
    public String md5;

    /**
     * 方法名
     */
    public String methodName;

    /**
     * 方法参数
     */
    public String parameters;

    /**
     * 是否已覆盖
     */
    public Boolean isCovered;

    /**
     * 方法第一行
     */
    public Integer firstLine;

    /**
     * 方法最后行
     */
    public Integer lastLine;

    /**
     * 报告方法行数据
     */
    public JSONArray reportLineList;

    /**
     * 拼接后的参数
     */
    public String parameterStr;


    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getParameters() {
        return parameters;
    }

    public void setParameters(String parameters) {
        this.parameters = parameters;
    }

    public Boolean getIsCovered() {
        return isCovered;
    }

    public void setIsCovered(Boolean isCovered) {
        this.isCovered = isCovered;
    }

    public JSONArray getReportLineList() {
        return reportLineList;
    }

    public void setReportLineList(JSONArray reportLineList) {
        this.reportLineList = reportLineList;
    }

    public Boolean getCovered() {
        return isCovered;
    }

    public void setCovered(Boolean covered) {
        isCovered = covered;
    }

    public Integer getFirstLine() {
        return firstLine;
    }

    public void setFirstLine(Integer firstLine) {
        this.firstLine = firstLine;
    }

    public Integer getLastLine() {
        return lastLine;
    }

    public void setLastLine(Integer lastLine) {
        this.lastLine = lastLine;
    }

    public String getParameterStr() {
        return parameterStr;
    }

    public void setParameterStr(String parameterStr) {
        this.parameterStr = parameterStr;
    }
}
