/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R<PERSON> Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.core.internal.diff;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import org.eclipse.jdt.core.dom.MethodDeclaration;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.diff.DiffEntry;
import org.eclipse.jgit.diff.DiffFormatter;
import org.eclipse.jgit.diff.Edit;
import org.eclipse.jgit.diff.EditList;
import org.eclipse.jgit.diff.RawTextComparator;
import org.eclipse.jgit.lib.ObjectId;
import org.eclipse.jgit.lib.ObjectReader;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.patch.FileHeader;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.treewalk.AbstractTreeIterator;
import org.eclipse.jgit.treewalk.CanonicalTreeParser;
import org.eclipse.jgit.util.StringUtils;

/**
 * 代码版本比较
 */
public class CodeDiff {

    public final static String REF_HEADS = "refs/heads/";

    public final static String MASTER = "master";

    /**
     * 分支和分支之间的比较
     *
     * @param gitPath       git路径
     * @param newBranchName 新分支名称
     * @param oldBranchName 旧分支名称
     * @return
     */
    public static List<ClassInfo> diffBranchToBranch(String gitPath, String newBranchName, String oldBranchName) {
        List<ClassInfo> classInfos = diffMethods(gitPath, newBranchName, oldBranchName);
        return classInfos;
    }

    private static List<ClassInfo> diffMethods(String gitPath, String newBranchName, String oldBranchName) {
        try {
            //  获取本地分支
            GitAdapter gitAdapter = new GitAdapter(gitPath);
            Git git = gitAdapter.getGit();
            Ref localBranchRef = gitAdapter.getRepository().exactRef(REF_HEADS + newBranchName);
            Ref localMasterRef = gitAdapter.getRepository().exactRef(REF_HEADS + oldBranchName);
            //  更新本地分支
            gitAdapter.checkOutAndPull(localMasterRef, oldBranchName);
            gitAdapter.checkOutAndPull(localBranchRef, newBranchName);
            //  获取分支信息
            AbstractTreeIterator oldTreeParser = gitAdapter.prepareTreeParser(localMasterRef);
            AbstractTreeIterator newTreeParser = gitAdapter.prepareTreeParser(localBranchRef);
            //  对比差异
            List<DiffEntry> diffs = git.diff().setOldTree(oldTreeParser).setNewTree(newTreeParser)
                    .setShowNameAndStatusOnly(true).call();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            DiffFormatter df = new DiffFormatter(out);
            //设置比较器为忽略空白字符对比（Ignores all whitespace）
            df.setDiffComparator(RawTextComparator.WS_IGNORE_ALL);
            df.setRepository(git.getRepository());
            List<ClassInfo> allClassInfos = batchPrepareDiffMethod(gitAdapter, newBranchName, oldBranchName, df, diffs);
            return allClassInfos;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<ClassInfo>();
    }


    /**
     * 单分支commit-id版本之间的比较
     *
     * @param gitPath     本地git代码仓库路径
     * @param branchName  分支名
     * @param newCommitId
     * @param oldCommitId
     * @return
     */
    public static List<ClassInfo> diffBranchToBranchCommitId(String gitPath, String branchName, String newCommitId,
            String oldCommitId, String lastCommitId, JSONObject object) {
        if (StringUtils.isEmptyOrNull(gitPath) || StringUtils.isEmptyOrNull(branchName) || StringUtils
                .isEmptyOrNull(newCommitId) || StringUtils.isEmptyOrNull(oldCommitId)) {
            throw new IllegalArgumentException(
                    "Parameter(local gitPath,develop branchName,new Tag,old Tag) can't be empty or null !");
        } else if (newCommitId.equals(oldCommitId)) {
            throw new IllegalArgumentException("当前commitId与基准commitId一致，请确认是否有代码改动。");
        }
        File gitPathDir = new File(gitPath);
        if (!gitPathDir.exists()) {
            throw new IllegalArgumentException("Parameter local gitPath is not exit !");
        }

        List<ClassInfo> classInfos = diffMethodsCommitId(gitPath, branchName, newCommitId, oldCommitId, lastCommitId,
                object);
        return classInfos;
    }

    public static List<ClassInfo> diffMethodsCommitId(String gitPath, String branchName, String newCommitId,
            String oldCommitId, String lastCommitId, JSONObject object) {
        Git git = null;
        try {
            GitAdapter gitAdapter = new GitAdapter(gitPath);
            git = gitAdapter.getGit();
            Repository repository = gitAdapter.getRepository();
            Ref localBranchRef = repository.exactRef(REF_HEADS + branchName);
            gitAdapter.checkOutAndPull(localBranchRef, branchName);


            RevWalk walk = new RevWalk(repository);
//        List<RevCommit> commitList = new ArrayList<>();
//            //获取最近提交的两次记录
//        Iterable<RevCommit> commits = git.log().setMaxCount(2).call();
//        for(RevCommit commit:commits){
//            commitList.add(commit);
//            System.out.println("commit    " + commit);
//            System.out.println("commit.toObjectId()    " + commit.toObjectId());
//            System.out.println(" commit.getAuthorIdent().getName()         " + commit.getAuthorIdent().getName());
//            System.out.println("" + commit.getAuthorIdent().getWhen());
//            System.out.println(" commit.getFullMessage())--- " + commit.getFullMessage());
//        }
//        if(commitList.size()==2){
//
//
//        }
            ObjectId newVersionId = repository.resolve(newCommitId);
            ObjectId oldVersionId = repository.resolve(oldCommitId);
            RevCommit newVerCommit = walk.parseCommit(newVersionId);
            RevCommit oldVerCommit = walk.parseCommit(oldVersionId);

            AbstractTreeIterator newTree = gitAdapter.commitIdPrepareTreeParser(newVerCommit);
            AbstractTreeIterator oldTree = gitAdapter.commitIdPrepareTreeParser(oldVerCommit);
            //  对比差异
            List<DiffEntry> diff = git.diff().setOldTree(oldTree).setNewTree(newTree).setShowNameAndStatusOnly(true)
                    .call();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            DiffFormatter df = new DiffFormatter(out);
            //设置比较器为忽略空白字符对比（Ignores all whitespace）
            df.setDiffComparator(RawTextComparator.WS_IGNORE_ALL);
            df.setRepository(git.getRepository());

            List<ClassInfo> allClassInfos = batchPrepareDiffMethodForTag(gitAdapter, newCommitId, oldCommitId,
                    lastCommitId, df, diff, object);

            return allClassInfos;

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != git) {
                git.getRepository().close();
            }
        }
        return new ArrayList<ClassInfo>();
    }


    /**
     * 单分支Tag版本之间的比较
     *
     * @param gitPath 本地git代码仓库路径
     * @param newTag  新Tag版本
     * @param oldTag  旧Tag版本
     * @return
     */
    public static List<ClassInfo> diffTagToTag(String gitPath, String branchName, String newTag, String oldTag) {
        if (StringUtils.isEmptyOrNull(gitPath) || StringUtils.isEmptyOrNull(branchName) || StringUtils
                .isEmptyOrNull(newTag) || StringUtils.isEmptyOrNull(oldTag)) {
            throw new IllegalArgumentException(
                    "Parameter(local gitPath,develop branchName,new Tag,old Tag) can't be empty or null !");
        } else if (newTag.equals(oldTag)) {
            throw new IllegalArgumentException("Parameter new Tag and old Tag can't be the same");
        }
        File gitPathDir = new File(gitPath);
        if (!gitPathDir.exists()) {
            throw new IllegalArgumentException("Parameter local gitPath is not exit !");
        }

        List<ClassInfo> classInfos = diffTagMethods(gitPath, branchName, newTag, oldTag);
        return classInfos;
    }

    private static List<ClassInfo> diffTagMethods(String gitPath, String branchName, String newTag, String oldTag) {
        try {
            //  init local repository
            GitAdapter gitAdapter = new GitAdapter(gitPath);
            Git git = gitAdapter.getGit();
            Repository repo = gitAdapter.getRepository();
            Ref localBranchRef = repo.exactRef(REF_HEADS + branchName);

            //  update local repository
            gitAdapter.checkOutAndPull(localBranchRef, branchName);

            ObjectId head = repo.resolve(newTag + "^{tree}");
            ObjectId previousHead = repo.resolve(oldTag + "^{tree}");

            // Instanciate a reader to read the data from the Git database
            ObjectReader reader = repo.newObjectReader();
            // Create the tree iterator for each commit
            CanonicalTreeParser oldTreeIter = new CanonicalTreeParser();
            oldTreeIter.reset(reader, previousHead);
            CanonicalTreeParser newTreeIter = new CanonicalTreeParser();
            newTreeIter.reset(reader, head);

            //  对比差异
            List<DiffEntry> diffs = git.diff().setOldTree(oldTreeIter).setNewTree(newTreeIter)
                    .setShowNameAndStatusOnly(true).call();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            DiffFormatter df = new DiffFormatter(out);
            //设置比较器为忽略空白字符对比（Ignores all whitespace）
            df.setDiffComparator(RawTextComparator.WS_IGNORE_ALL);
            df.setRepository(repo);
            List<ClassInfo> allClassInfos = batchPrepareDiffMethodForTag(gitAdapter, newTag, oldTag, null, df, diffs,
                    null);
            return allClassInfos;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ArrayList<ClassInfo>();
    }

    /**
     * 多线程执行对比
     *
     * @return
     */
    private static List<ClassInfo> batchPrepareDiffMethodForTag(final GitAdapter gitAdapter, final String newTag,
            final String oldTag, final String lastTag, final DiffFormatter df, List<DiffEntry> diffs,
            final JSONObject object) {
        int threadSize = 100;
        int dataSize = diffs.size();
        int threadNum = dataSize / threadSize + 1;
        boolean special = dataSize % threadSize == 0;
        ExecutorService executorService = Executors.newFixedThreadPool(threadNum);

        List<Callable<List<ClassInfo>>> tasks = new ArrayList<Callable<List<ClassInfo>>>();
        Callable<List<ClassInfo>> task = null;
        List<DiffEntry> cutList = null;
        //  分解每条线程的数据
        for (int i = 0; i < threadNum; i++) {
            if (i == threadNum - 1) {
                if (special) {
                    break;
                }
                cutList = diffs.subList(threadSize * i, dataSize);
            } else {
                cutList = diffs.subList(threadSize * i, threadSize * (i + 1));
            }
            final List<DiffEntry> diffEntryList = cutList;
            task = new Callable<List<ClassInfo>>() {
                @Override
                public List<ClassInfo> call() throws Exception {
                    List<ClassInfo> allList = new ArrayList<ClassInfo>();
                    for (DiffEntry diffEntry : diffEntryList) {
                        ClassInfo classInfo = prepareDiffMethodForTag(gitAdapter, newTag, oldTag, lastTag, df,
                                diffEntry, object);
                        if (classInfo != null) {
                            allList.add(classInfo);
                        }
                    }
                    return allList;
                }
            };
            // 这里提交的任务容器列表和返回的Future列表存在顺序对应的关系
            tasks.add(task);
        }
        List<ClassInfo> allClassInfoList = new ArrayList<ClassInfo>();
        try {
            List<Future<List<ClassInfo>>> results = executorService.invokeAll(tasks);
            //结果汇总
            for (Future<List<ClassInfo>> future : results) {
                allClassInfoList.addAll(future.get());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭线程池
            executorService.shutdown();
        }
        return allClassInfoList;
    }

    /**
     * 单个差异文件对比
     *
     * @param gitAdapter
     * @param newTag
     * @param oldTag
     * @param df
     * @param diffEntry
     * @return
     */
    private synchronized static ClassInfo prepareDiffMethodForTag(GitAdapter gitAdapter, String newTag, String oldTag,
            String lastTag, DiffFormatter df, DiffEntry diffEntry, JSONObject object) {
        List<MethodInfo> methodInfoList = new ArrayList<MethodInfo>();
        try {
            String newJavaPath = diffEntry.getNewPath();

            //  排除测试类
            if (newJavaPath.contains("/src/test/java/")) {
                return null;
            }
            //  非java文件 和 删除类型不记录
            if (!newJavaPath.endsWith(".java") || diffEntry.getChangeType() == DiffEntry.ChangeType.DELETE) {
                return null;
            }
            String newClassContent = gitAdapter.getTagRevisionSpecificFileContent(newTag, newJavaPath);
            ASTGenerator newAstGenerator = new ASTGenerator(newClassContent);
            /*  新增类型   */
            if (diffEntry.getChangeType() == DiffEntry.ChangeType.ADD) {
                return newAstGenerator.getClassInfo();
            }
            /*  修改类型  */
            //  获取文件差异位置，从而统计差异的行数，如增加行数，减少行数
            FileHeader fileHeader = df.toFileHeader(diffEntry);
            List<int[]> addLines = new ArrayList<int[]>();
            List<int[]> delLines = new ArrayList<int[]>();
            EditList editList = fileHeader.toEditList();
            for (Edit edit : editList) {
                if (edit.getLengthA() > 0) {
                    delLines.add(new int[]{edit.getBeginA(), edit.getEndA()});
                }
                if (edit.getLengthB() > 0) {
                    addLines.add(new int[]{edit.getBeginB(), edit.getEndB()});
                }
            }
            String oldJavaPath = diffEntry.getOldPath();
            String oldClassContent = gitAdapter.getTagRevisionSpecificFileContent(oldTag, oldJavaPath);
            ASTGenerator oldAstGenerator = new ASTGenerator(oldClassContent);
            MethodDeclaration[] newMethods = newAstGenerator.getMethods();
            MethodDeclaration[] oldMethods = oldAstGenerator.getMethods();

            Map<String, MethodDeclaration> oldMethodsMap = new HashMap<String, MethodDeclaration>();
            for (int i = 0; i < oldMethods.length; i++) {
                oldMethodsMap
                        .put(oldMethods[i].getName().toString() + oldMethods[i].parameters().toString(), oldMethods[i]);
            }
            String className = null;
            if (null != newAstGenerator.getClassInfo()) {
                className = newAstGenerator.getClassInfo().getClassName();
            } else {
                if (null != newAstGenerator.getJavaClass()) {
                    className = newAstGenerator.getJavaClass().getName().toString();
                }
            }
            for (final MethodDeclaration method : newMethods) {
                // 如果方法名是新增的,则直接将方法加入List
                if (!ASTGenerator.isMethodExist(method, oldMethodsMap)) {
                    MethodInfo methodInfo = newAstGenerator.getMethodInfo(method);
                    if (!StringUtils.isEmptyOrNull(lastTag)) {
                        if (!isMethodExistInLastCommit(method, lastTag, gitAdapter, oldJavaPath)) {
                            methodInfo.setIsCovered(false);
                        } else {
                            isCoveredByLastCommit(object,
                                    newAstGenerator.getPackageName(),
                                    className, method,
                                    gitAdapter, lastTag, oldJavaPath, methodInfo);
                        }
                    }
                    methodInfoList.add(methodInfo);
                    continue;
                }
                // 如果两个版本都有这个方法,则根据MD5判断方法是否一致
                if (!ASTGenerator.isMethodTheSame(method,
                        oldMethodsMap.get(method.getName().toString() + method.parameters().toString()))) {
                    MethodInfo methodInfo = newAstGenerator.getMethodInfo(method);
                    if (!StringUtils.isEmptyOrNull(lastTag)) {
                        isCoveredByLastCommit(object,
                                newAstGenerator.getPackageName(),
                                newAstGenerator.getClassInfo() != null ? newAstGenerator.getClassInfo().getClassName()
                                        : null, method,
                                gitAdapter, lastTag, oldJavaPath, methodInfo);
                    }
                    methodInfoList.add(methodInfo);
                }
            }
            return newAstGenerator.getClassInfo(methodInfoList, addLines, delLines);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 多线程执行对比
     *
     * @return
     */
    private static List<ClassInfo> batchPrepareDiffMethod(final GitAdapter gitAdapter, final String branchName,
            final String oldBranchName, final DiffFormatter df, List<DiffEntry> diffs) {
        int threadSize = 100;
        int dataSize = diffs.size();
        int threadNum = dataSize / threadSize + 1;
        boolean special = dataSize % threadSize == 0;
        ExecutorService executorService = Executors.newFixedThreadPool(threadNum);

        List<Callable<List<ClassInfo>>> tasks = new ArrayList<Callable<List<ClassInfo>>>();
        Callable<List<ClassInfo>> task = null;
        List<DiffEntry> cutList = null;
        //  分解每条线程的数据
        for (int i = 0; i < threadNum; i++) {
            if (i == threadNum - 1) {
                if (special) {
                    break;
                }
                cutList = diffs.subList(threadSize * i, dataSize);
            } else {
                cutList = diffs.subList(threadSize * i, threadSize * (i + 1));
            }
            final List<DiffEntry> diffEntryList = cutList;
            task = new Callable<List<ClassInfo>>() {
                @Override
                public List<ClassInfo> call() throws Exception {
                    List<ClassInfo> allList = new ArrayList<ClassInfo>();
                    for (DiffEntry diffEntry : diffEntryList) {
                        ClassInfo classInfo = prepareDiffMethod(gitAdapter, branchName, oldBranchName, df, diffEntry);
                        if (classInfo != null) {
                            allList.add(classInfo);
                        }
                    }
                    return allList;
                }
            };
            // 这里提交的任务容器列表和返回的Future列表存在顺序对应的关系
            tasks.add(task);
        }
        List<ClassInfo> allClassInfoList = new ArrayList<ClassInfo>();
        try {
            List<Future<List<ClassInfo>>> results = executorService.invokeAll(tasks);
            //结果汇总
            for (Future<List<ClassInfo>> future : results) {
                allClassInfoList.addAll(future.get());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭线程池
            executorService.shutdown();
        }
        return allClassInfoList;
    }

    /**
     * 单个差异文件对比
     *
     * @param gitAdapter
     * @param branchName
     * @param oldBranchName
     * @param df
     * @param diffEntry
     * @return
     */
    private synchronized static ClassInfo prepareDiffMethod(GitAdapter gitAdapter, String branchName,
            String oldBranchName, DiffFormatter df, DiffEntry diffEntry) {
        List<MethodInfo> methodInfoList = new ArrayList<MethodInfo>();
        try {
            String newJavaPath = diffEntry.getNewPath();
            //  排除测试类
            if (newJavaPath.contains("/src/test/java/")) {
                return null;
            }
            //  非java文件 和 删除类型不记录
            if (!newJavaPath.endsWith(".java") || diffEntry.getChangeType() == DiffEntry.ChangeType.DELETE) {
                return null;
            }
            String newClassContent = gitAdapter.getBranchSpecificFileContent(branchName, newJavaPath);
            ASTGenerator newAstGenerator = new ASTGenerator(newClassContent);
            /*  新增类型   */
            if (diffEntry.getChangeType() == DiffEntry.ChangeType.ADD) {
                return newAstGenerator.getClassInfo();
            }
            /*  修改类型  */
            //  获取文件差异位置，从而统计差异的行数，如增加行数，减少行数
            FileHeader fileHeader = df.toFileHeader(diffEntry);
            List<int[]> addLines = new ArrayList<int[]>();
            List<int[]> delLines = new ArrayList<int[]>();
            EditList editList = fileHeader.toEditList();
            for (Edit edit : editList) {
                if (edit.getLengthA() > 0) {
                    delLines.add(new int[]{edit.getBeginA(), edit.getEndA()});
                }
                if (edit.getLengthB() > 0) {
                    addLines.add(new int[]{edit.getBeginB(), edit.getEndB()});
                }
            }
            String oldJavaPath = diffEntry.getOldPath();
            String oldClassContent = gitAdapter.getBranchSpecificFileContent(oldBranchName, oldJavaPath);
            ASTGenerator oldAstGenerator = new ASTGenerator(oldClassContent);
            MethodDeclaration[] newMethods = newAstGenerator.getMethods();
            MethodDeclaration[] oldMethods = oldAstGenerator.getMethods();
            Map<String, MethodDeclaration> methodsMap = new HashMap<String, MethodDeclaration>();
            for (int i = 0; i < oldMethods.length; i++) {
                methodsMap
                        .put(oldMethods[i].getName().toString() + oldMethods[i].parameters().toString(), oldMethods[i]);
            }
            for (final MethodDeclaration method : newMethods) {
                // 如果方法名是新增的,则直接将方法加入List
                if (!ASTGenerator.isMethodExist(method, methodsMap)) {
                    MethodInfo methodInfo = newAstGenerator.getMethodInfo(method);
                    methodInfoList.add(methodInfo);
                    continue;
                }
                // 如果两个版本都有这个方法,则根据MD5判断方法是否一致
                if (!ASTGenerator.isMethodTheSame(method,
                        methodsMap.get(method.getName().toString() + method.parameters().toString()))) {
                    MethodInfo methodInfo = newAstGenerator.getMethodInfo(method);
                    methodInfoList.add(methodInfo);
                }
            }
            return newAstGenerator.getClassInfo(methodInfoList, addLines, delLines);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static boolean isMethodExistInLastCommit(MethodDeclaration method, String lastCommitId,
            GitAdapter gitAdapter, String oldJavaPath)
            throws IOException {
        String lastClassContent = gitAdapter
                .getTagRevisionSpecificFileContent(lastCommitId, oldJavaPath);
        ASTGenerator lastAstGenerator = new ASTGenerator(lastClassContent);
        MethodDeclaration[] lastMethods = lastAstGenerator.getMethods();
        Map<String, MethodDeclaration> methodsMap = new HashMap<>();
        for (int i = 0; i < lastMethods.length; i++) {
            methodsMap
                    .put(lastMethods[i].getName().toString() + lastMethods[i].parameters().toString(), lastMethods[i]);
        }
        if (ASTGenerator.isMethodExist(method, methodsMap)) {
            return true;
        }
        return false;
    }

    private static void isCoveredByLastCommit(JSONObject reportObject, String packageName, String className,
            MethodDeclaration method, GitAdapter gitAdapter, String lastTag, String oldJavaPath, MethodInfo methodInfo) {
        if (null == className) {
            return;
        }
        if (null == reportObject) {
            methodInfo.setIsCovered(false);
            return;
        }
        String lastClassContent = null;
        try {
            lastClassContent = gitAdapter.getTagRevisionSpecificFileContent(lastTag, oldJavaPath);
        } catch (IOException e) {
            e.printStackTrace();
        }
        ASTGenerator lastAstGenerator = new ASTGenerator(lastClassContent);
        MethodDeclaration[] lastMethods = lastAstGenerator.getMethods();
        Map<String, MethodDeclaration> lastMethodsMap = new HashMap<String, MethodDeclaration>();
        for (int i = 0; i < lastMethods.length; i++) {
            lastMethodsMap
                    .put(lastMethods[i].getName().toString() + lastMethods[i].parameters().toString(), lastMethods[i]);
        }
        for (int i = 0; i < reportObject.size(); i++) {
            JSONArray packageDtoList = reportObject.getJSONArray("packageDtoList");
            if (null == packageDtoList) {
                methodInfo.setIsCovered(false);
            }
            for (int j = 0; j < packageDtoList.size(); j++) {
                JSONObject packageDto = packageDtoList.getJSONObject(j);
                if (!packageName.equals(packageDto.getString("name").replace("/", "."))) {
                    continue;
                }
                JSONArray classDtoList = packageDto.getJSONArray("classDtoList");
                if (null == classDtoList) {
                    methodInfo.setIsCovered(false);
                    return;
                }
                for (int b = 0; b < classDtoList.size(); b++) {
                    JSONObject classDto = classDtoList.getJSONObject(b);
                    if (!className.equals(classDto.getString("sourceFileName").split("\\.")[0])) {
                        continue;
                    }
                    JSONArray methodDtoList = classDto.getJSONArray("reportMethodDtoList");
                    if (null == methodDtoList) {
                        methodInfo.setIsCovered(false);
                        return;
                    }
                    for (int l = 0; l < methodDtoList.size(); l++) {
                        JSONObject methodDto = methodDtoList.getJSONObject(l);
                        if (!method.getName().toString().equals(methodDto.getString("name"))) {
                            continue;
                        }
                        methodInfo.setReportLineList(methodDto.getJSONArray("reportLineDtoList"));
                        JSONArray methodReportCoveredDtoList = methodDto.getJSONArray("reportCoveredDtoList");
                        for (int m = 0; m < methodReportCoveredDtoList.size(); m++) {
                            JSONObject methodReportCoveredDto = methodReportCoveredDtoList.getJSONObject(m);
                            String coveredName = methodReportCoveredDto.getString("name");
                            if (!coveredName.equals("INSTRUCTION")) {
                                continue;
                            }
                            if (methodReportCoveredDto.getInteger("missed") == 0 && ASTGenerator.isMethodTheSame(method,
                                    lastMethodsMap.get(method.getName().toString() + method.parameters().toString()))) {
                                methodInfo.setIsCovered(true);
                                return;
                            } else {
                                methodInfo.setIsCovered(false);
                                return;
                            }
                        }
                    }
                }
            }
        }
    }

    private static void isCoveredByOtherCommit(List<MethodInfo> methodInfoList, ASTGenerator newAstGenerator,
            List<String> commitIdList, MethodDeclaration method, GitAdapter gitAdapter, String oldJavaPath)
            throws IOException {
        final Map<String, Object> coveredMethodMap = new HashMap<>();
        for (String commitId : commitIdList) {
            String tempClassContent = gitAdapter
                    .getTagRevisionSpecificFileContent(commitId, oldJavaPath);
            ASTGenerator tempAstGenerator = new ASTGenerator(tempClassContent);
            MethodDeclaration[] tempMethods = tempAstGenerator.getMethods();
            for (MethodDeclaration tempMethod : tempMethods) {
                MethodInfo newMethodInfo = newAstGenerator.getMethodInfo(method);
                MethodInfo historyMethodInfo = tempAstGenerator.getMethodInfo(tempMethod);
                if (!newMethodInfo.getMethodName().equals(historyMethodInfo.getMethodName())) {
                    continue;
                }
                if (!ASTGenerator.isMethodTheSame(method, tempMethod)) {
                    if (null == methodInfoList || methodInfoList.size() == 0) {
                        if (!coveredMethodMap.containsKey(newMethodInfo.getMethodName())) {
                            methodInfoList.add(newMethodInfo);
                            continue;
                        }
                    }
                    for (MethodInfo tempMethodInfo : methodInfoList) {
                        if (!historyMethodInfo.getMethodName().equals(tempMethodInfo.getMethodName())) {
                            if (!coveredMethodMap.containsKey(newMethodInfo.getMethodName())) {
                                methodInfoList.add(newMethodInfo);
                                break;
                            }
                        }
                    }
                } else {
                    coveredMethodMap.put(newMethodInfo.getMethodName(), newMethodInfo);
                }
            }
        }
    }


}
