/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Marc R. Hoffmann - initial API and implementation
 *
 *******************************************************************************/
package org.jacoco.core.internal.analysis;

import com.alibaba.fastjson.JSONObject;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;
import org.jacoco.core.analysis.CoverageBuilder;
import org.jacoco.core.internal.analysis.filter.Filters;
import org.jacoco.core.internal.analysis.filter.IFilter;
import org.jacoco.core.internal.analysis.filter.IFilterContext;
import org.jacoco.core.internal.diff.ClassInfo;
import org.jacoco.core.internal.diff.MethodInfo;
import org.jacoco.core.internal.flow.ClassProbesVisitor;
import org.jacoco.core.internal.flow.MethodProbesVisitor;
import org.jacoco.core.internal.instr.InstrSupport;
import org.jacoco.core.tools.ILanguageNames;
import org.jacoco.core.tools.JavaNames;
import org.objectweb.asm.AnnotationVisitor;
import org.objectweb.asm.FieldVisitor;
import org.objectweb.asm.MethodVisitor;
import org.objectweb.asm.tree.MethodNode;

/**
 * Analyzes the structure of a class.
 */
@Slf4j
public class ClassAnalyzer extends ClassProbesVisitor
		implements IFilterContext {

	private final ClassCoverageImpl coverage;
	private final boolean[] probes;
	private final StringPool stringPool;

	private final Set<String> classAnnotations = new HashSet<String>();

	private String sourceDebugExtension;

	private final IFilter filter;

	private ILanguageNames javaNames = new JavaNames();

	/**
	 * Creates a new analyzer that builds coverage data for a class.
	 *
	 * @param coverage
	 *            coverage node for the analyzed class data
	 * @param probes
	 *            execution data for this class or <code>null</code>
	 * @param stringPool
	 *            shared pool to minimize the number of {@link String} instances
	 */
	public ClassAnalyzer(final ClassCoverageImpl coverage,
			final boolean[] probes, final StringPool stringPool) {
		this.coverage = coverage;
		this.probes = probes;
		this.stringPool = stringPool;
		this.filter = Filters.all();
	}

	@Override
	public void visit(final int version, final int access, final String name,
			final String signature, final String superName,
			final String[] interfaces) {
		coverage.setSignature(stringPool.get(signature));
		coverage.setSuperName(stringPool.get(superName));
		coverage.setInterfaces(stringPool.get(interfaces));
	}

	@Override
	public AnnotationVisitor visitAnnotation(final String desc,
			final boolean visible) {
		classAnnotations.add(desc);
		return super.visitAnnotation(desc, visible);
	}

	@Override
	public void visitSource(final String source, final String debug) {
		coverage.setSourceFileName(stringPool.get(source));
		sourceDebugExtension = debug;
	}

	@Override
	public MethodProbesVisitor visitMethod(int access, String name, String desc, String signature,
			String[] exceptions) {
		return null;
	}

	@Override
	public MethodProbesVisitor visitMethod(final int access, final String name,
			final String desc, final String signature,
			final String[] exceptions, final JSONObject object, final List<ClassInfo> classInfos) {

		InstrSupport.assertNotInstrumented(name, coverage.getName());

		final InstructionsBuilder builder = new InstructionsBuilder(probes);

		return new MethodAnalyzer(builder) {

			@Override
			public void accept(final MethodNode methodNode,
					final MethodVisitor methodVisitor) {
				super.accept(methodNode, methodVisitor);
				addMethodCoverage(stringPool.get(name), stringPool.get(desc),
						stringPool.get(signature), builder, methodNode, object, classInfos);
			}
		};
	}

	private void addMethodCoverage(final String name, final String desc,
			final String signature, final InstructionsBuilder icc,
			final MethodNode methodNode, final JSONObject object, final List<ClassInfo> classInfos) {

        boolean isCovered = false;
        if (null != CoverageBuilder.classInfos) {
			for (ClassInfo classInfo : CoverageBuilder.classInfos) {
				String className = classInfo.getPackages().replace(".","/") + "/" + classInfo.getClassName();
				if (!className.equals(this.getClassName())) {
					continue;
				}
				for (MethodInfo methodInfo : classInfo.getMethodInfos()) {
					if (isTheSameMethod(methodInfo, methodNode)) {
						isCovered = methodInfo.getIsCovered() != null ? methodInfo.getIsCovered() : false;
						break;
					}
				}
				break;
			}
		}

		final MethodCoverageCalculator mcc = new MethodCoverageCalculator(
				icc.getInstructions());
		filter.filter(methodNode, this, mcc);

		final MethodCoverageImpl mc = new MethodCoverageImpl(name, desc,
				signature);
		mcc.calculate(mc, this.coverage.getName(), object, isCovered);
		if (mc.containsCode()) {
			// Only consider methods that actually contain code
			coverage.addMethod(mc);
		}

	}

	@Override
	public FieldVisitor visitField(final int access, final String name,
			final String desc, final String signature, final Object value) {
		InstrSupport.assertNotInstrumented(name, coverage.getName());
		return super.visitField(access, name, desc, signature, value);
	}

	@Override
	public void visitTotalProbeCount(final int count) {
		// nothing to do
	}

	// IFilterContext implementation

	public String getClassName() {
		return coverage.getName();
	}

	public String getSuperClassName() {
		return coverage.getSuperName();
	}

	public Set<String> getClassAnnotations() {
		return classAnnotations;
	}

	public String getSourceFileName() {
		return coverage.getSourceFileName();
	}

	public String getSourceDebugExtension() {
		return sourceDebugExtension;
	}

	private boolean isTheSameMethod(MethodInfo methodInfo, MethodNode methodNode) {
		String method1 = methodInfo.getMethodName() + methodInfo.getParameterStr();
//		String method2 = methodNode.name + handlerParam(methodNode.desc);
		String method2 = javaNames.getMethodName(this.getClassName(), methodNode.name, methodNode.desc, null);
		if (method1.equals(method2)) {
			return true;
		}
		return false;
	}

	private static String handlerParam(String desc) {
		if (null == desc || "".equals(desc)) {
			return "()";
		}
		if (desc.startsWith("(")) {
			int angleBracketIndex = desc.indexOf(")");
			desc = desc.substring(1, angleBracketIndex);
		}
		if (null == desc || "".equals(desc)) {
			return "()";
		}
		boolean comma = false;
		StringBuilder sb = new StringBuilder();
		sb.append("(");
		String[] params = desc.split(";");
		for (String param : params) {
			if (comma) {
				sb.append(",");
			} else {
				comma = true;
			}
			typeSignature2Java(param, sb);
		}
		sb.append(")");
		return sb.toString();
	}

	/**
	 * JVM类型转换
	 *
	 * @return
	 */
	private static void typeSignature2Java(String param, StringBuilder sb) {
		if (param.startsWith("Z")) {
			sb.append("boolean");
			param = param.replaceFirst("Z", "");
		} else if (param.startsWith("B")) {
			sb.append("byte");
			param = param.replaceFirst("B", "");
		} else if (param.startsWith("C")) {
			sb.append("char");
			param = param.replaceFirst("C", "");
		} else if (param.startsWith("S")) {
			sb.append("short");
			param = param.replaceFirst("S", "");
		} else if (param.startsWith("I")) {
			sb.append("int");
			param = param.replaceFirst("I", "");
		} else if (param.startsWith("J")) {
			sb.append("long");
			param = param.replaceFirst("J", "");
		} else if (param.startsWith("F")) {
			sb.append("float");
			param = param.replaceFirst("F", "");
		} else if (param.startsWith("D")) {
			sb.append("double");
			param = param.replaceFirst("D", "");
		} else if (param.startsWith("[Z")) {
			sb.append("boolean[]");
			param = param.replaceFirst("\\[Z", "");
		} else if (param.startsWith("[B")) {
			sb.append("byte[]");
			param = param.replaceFirst("\\[B", "");
		} else if (param.startsWith("[C")) {
			sb.append("char[]");
			param = param.replaceFirst("\\[C", "");
		} else if (param.startsWith("[S")) {
			sb.append("short[]");
			param = param.replaceFirst("\\[S", "");
		} else if (param.startsWith("[I")) {
			sb.append("int[]");
			param = param.replaceFirst("\\[I", "");
		} else if (param.startsWith("[J")) {
			sb.append("long[]");
			param = param.replaceFirst("\\[J", "");
		} else if (param.startsWith("[F")) {
			sb.append("float[]");
			param = param.replaceFirst("\\[F", "");
		} else if (param.startsWith("[D")) {
			sb.append("double[]");
			param = param.replaceFirst("\\[D", "");
		}
		if (param.startsWith("L")) {
			int lastIndex = param.lastIndexOf("/");
			param = param.substring(lastIndex + 1);
			if (param.contains("$")) {
				int last$ = param.lastIndexOf("$");
				param = param.substring(last$ + 1);
			}
			if (!sb.toString().equals("(") && !sb.toString().endsWith(",")) {
				sb.append(",");
			}
		}
		if (param.startsWith("[L")) {
			int lastIndex = param.lastIndexOf("/");
			param = param.substring(lastIndex + 1);
			if (param.contains("$")) {
				int last$ = param.lastIndexOf("$");
				param = param.substring(last$ + 1) + "[]";
			} else {
				param = param + "[]";
			}
			if (!sb.toString().equals("(") && !sb.toString().endsWith(",")) {
				sb.append(",");
			}
		}
		sb.append(param);
	}

}
