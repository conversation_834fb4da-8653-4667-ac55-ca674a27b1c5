<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.build</artifactId>
    <version>0.8.4</version>
    <relativePath>../org.jacoco.build</relativePath>
  </parent>

  <artifactId>jacoco</artifactId>
  <packaging>pom</packaging>

  <name>JaCoCo :: Distribution</name>
  <description>JaCoCo Standalone Distribution</description>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.report</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent</artifactId>
      <version>${project.version}</version>
      <classifier>runtime</classifier>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.ant</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.ant</artifactId>
      <classifier>nodeps</classifier>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.cli</artifactId>
      <version>${project.version}</version>
      <classifier>nodeps</classifier>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.examples</artifactId>
      <version>${project.version}</version>
      <type>zip</type>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.doc</artifactId>
      <version>${project.version}</version>
      <classifier>javadoc</classifier>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <finalName>jacoco-${qualified.bundle.version}</finalName>
              <appendAssemblyId>false</appendAssemblyId>
              <descriptors>
                <descriptor>assembly.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>enforce-distribution-size</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>verify</phase>
            <configuration>
              <rules>
                <requireFilesSize>
                  <maxsize>4400000</maxsize>
                  <minsize>3400000</minsize>
                  <files>
                    <file>${project.build.directory}/jacoco-${qualified.bundle.version}.zip</file>
                  </files>
                </requireFilesSize>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
