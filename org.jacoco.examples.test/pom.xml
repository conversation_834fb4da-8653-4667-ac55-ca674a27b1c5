<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.tests</artifactId>
    <version>0.8.4</version>
    <relativePath>../org.jacoco.tests</relativePath>
  </parent>

  <artifactId>org.jacoco.examples.test</artifactId>

  <name>JaCoCo :: Test :: Examples</name>

  <properties>
    <jacoco.includes>org.jacoco.examples.*</jacoco.includes>
    <jacoco.excludes>org.jacoco.examples.CoreTutorial$TestTarget</jacoco.excludes>
  </properties>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.examples</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent.rt</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>default-unpack</id>
            <phase>package</phase>
            <goals>
              <goal>unpack</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>${project.groupId}</groupId>
                  <artifactId>org.jacoco.examples</artifactId>
                  <version>${project.version}</version>
                  <type>zip</type>
                  <overWrite>true</overWrite>
                  <outputDirectory>${project.build.directory}/build</outputDirectory>
                </artifactItem>
              </artifactItems>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-invoker-plugin</artifactId>
        <executions>
          <execution>
            <id>test-pom</id>
            <goals>
              <goal>install</goal>
              <goal>run</goal>
            </goals>
            <configuration>
              <cloneProjectsTo>${project.build.directory}/it</cloneProjectsTo>
              <postBuildHookScript>../../../src/test/resources/verify</postBuildHookScript>
              <goals>
                <goal>verify</goal>
              </goals>
            </configuration>
          </execution>
          <execution>
            <id>test-pom-it</id>
            <goals>
              <goal>install</goal>
              <goal>run</goal>
            </goals>
            <configuration>
              <cloneProjectsTo>${project.build.directory}/it-it</cloneProjectsTo>
              <postBuildHookScript>../../../src/test/resources/verify-it</postBuildHookScript>
              <goals>
                <!-- this is probably a hack! -->
                <goal>-f pom-it.xml</goal>
                <goal>verify</goal>
              </goals>
            </configuration>
          </execution>
          <execution>
            <id>test-pom-offline</id>
            <goals>
              <goal>install</goal>
              <goal>run</goal>
            </goals>
            <configuration>
              <cloneProjectsTo>${project.build.directory}/it-offline</cloneProjectsTo>
              <postBuildHookScript>../../../src/test/resources/verify-offline</postBuildHookScript>
              <goals>
                <!-- this is probably a hack! -->
                <goal>-f pom-offline.xml</goal>
                <goal>verify</goal>
              </goals>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <projectsDirectory>${project.build.directory}/build/examples</projectsDirectory>
          <properties>
            <maven.compiler.source>${maven.compiler.source}</maven.compiler.source>
            <maven.compiler.target>${maven.compiler.target}</maven.compiler.target>
          </properties>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
