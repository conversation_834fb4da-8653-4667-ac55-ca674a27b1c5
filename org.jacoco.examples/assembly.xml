<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<assembly>
  <id>distribution</id>
  <formats>
    <format>zip</format>
  </formats>
  <includeBaseDirectory>false</includeBaseDirectory>
  <fileSets>
    <fileSet>
      <directory>${basedir}/build</directory>
      <outputDirectory>/examples/build</outputDirectory>
      <fileMode>0644</fileMode>
      <directoryMode>0755</directoryMode>
      <filtered>true</filtered>
    </fileSet>
    <fileSet>
      <directory>${basedir}/src/org/jacoco/examples</directory>
      <outputDirectory>/examples/java</outputDirectory>
      <fileMode>0644</fileMode>
      <directoryMode>0755</directoryMode>
    </fileSet>
  </fileSets>
</assembly>
