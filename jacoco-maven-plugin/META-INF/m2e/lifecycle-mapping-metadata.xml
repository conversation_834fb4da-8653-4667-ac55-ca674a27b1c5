<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<lifecycleMappingMetadata>
  <pluginExecutions>
    <pluginExecution>
      <pluginExecutionFilter>
        <goals>
          <goal>prepare-agent</goal>
          <goal>prepare-agent-integration</goal>
          <goal>merge</goal>
          <goal>report</goal>
          <goal>report-integration</goal>
          <goal>report-aggregate</goal>
          <goal>check</goal>
          <goal>dump</goal>
          <goal>instrument</goal>
          <goal>restore-instrumented-classes</goal>
        </goals>
      </pluginExecutionFilter>
      <action>
        <ignore />
      </action>
    </pluginExecution>
  </pluginExecutions>
</lifecycleMappingMetadata>
