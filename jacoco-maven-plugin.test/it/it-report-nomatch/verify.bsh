/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Evgeny <PERSON>ikov - initial API and implementation
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
if ( buildLog.indexOf( "Classes in bundle " ) < 0 || buildLog.indexOf(" do no match with execution data." ) < 0 ) {
    throw new RuntimeException( "Warning 1 was not printed" );
}
if ( buildLog.indexOf( "For report generation the same class files must be used as at runtime." ) < 0 ) {
    throw new RuntimeException( "Warning 2 was not printed" );
}
if ( buildLog.indexOf( "Execution data for class Example does not match." ) < 0 ) {
    throw new RuntimeException( "Warning 3 was not printed" );
}
