/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Evgeny Mandrikov - initial API and implementation
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String agentOptions = "jacoco.exec -ea";
String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
if ( buildLog.indexOf( agentOptions ) < 0 ) {
    throw new RuntimeException( "Property was not configured" );
}

File file = new File( basedir, "target/jacoco.exec" );
if ( !file.isFile() )
{
    throw new FileNotFoundException( "Could not find generated dump: " + file );
}
