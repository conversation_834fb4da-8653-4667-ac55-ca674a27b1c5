/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Evgeny Mandrikov - initial API and implementation
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
if ( buildLog.indexOf( "Skipping JaCoCo execution due to missing classes directory." ) < 0 ) {
    throw new RuntimeException( "Execution should be skipped when target/classes does not exist." );
}

File dump2 = new File( basedir, "skip-child/target/jacoco.exec" );
if ( dump2.isFile() ) {
    throw new RuntimeException( "Should not be executed for module 'skip-child', but dump found : " + dump2 );
}

if ( !buildLog.contains( "argLine set to empty" ) ) {
    throw new RuntimeException( "Property not set to empty when skipping." );
}

File file = new File( basedir, "child/target/jacoco.exec" );
if ( !file.isFile() )
{
    throw new FileNotFoundException( "Could not find generated dump: " + file );
}

File xmlReport = new File( basedir, "child/target/site/jacoco/jacoco.xml" );
if ( !xmlReport.isFile() )
{
    throw new FileNotFoundException( "Could not find generated XML report: " + xmlReport );
}

File csvReport = new File( basedir, "child/target/site/jacoco/jacoco.csv" );
if ( !csvReport.isFile() )
{
    throw new FileNotFoundException( "Could not find generated CSV report: " + csvReport );
}

File htmlReport = new File( basedir, "child/target/site/jacoco/index.html" );
if ( !htmlReport.isFile() )
{
    throw new FileNotFoundException( "Could not find generated HTML report: " + htmlReport );
}
