/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Ev<PERSON> - initial API and implementation
 *    <PERSON> - implementation of CheckMojo
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
if ( buildLog.indexOf( "Coverage checks have not been met." ) < 0 ) {
    throw new RuntimeException( "Coverage checks should not have been met." );
}

if ( buildLog.indexOf( "methods missed count is 1, but expected maximum is 0" ) < 0 ) {
    throw new RuntimeException( "Should have displayed insufficient code coverage messages." );
}
