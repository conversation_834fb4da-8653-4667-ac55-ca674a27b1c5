/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Evgeny <PERSON>ikov - initial API and implementation
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String agentOptions = "destfile=" + basedir + File.separator + "target" + File.separator + "coverage.exec"
    + ",append=false"
    + ",includes=*"
    + ",excludes=java.*:sun.*"
    + ",exclclassloader=sun.reflect.DelegatingClassLoader:MyClassLoader"
    + ",inclbootstrapclasses=true"
    + ",inclnolocationclasses=true"
    + ",sessionid=session"
    + ",dumponexit=true"
    + ",output=file"
    + ",address=localhost"
    + ",port=9999"
    + ",classdumpdir=" + basedir + File.separator + "target" + File.separator + "classdumps"
    + ",jmx=true";

//backslashes will be escaped
agentOptions = agentOptions.replace("\\","\\\\");
String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );
if ( buildLog.indexOf( agentOptions ) < 0 ) {
    throw new RuntimeException( "Property was not configured, expected " + agentOptions );
}

File file = new File( basedir, "target/coverage.exec" );
if ( !file.isFile() )
{
    throw new FileNotFoundException( "Could not find generated dump: " + file );
}

File reportDir = new File( basedir, "target/site/jacoco" );
if ( !reportDir.isDirectory() )
{
    throw new RuntimeException( "Could not find generated report" );
}
