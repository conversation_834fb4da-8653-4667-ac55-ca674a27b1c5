/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    Evgeny <PERSON>ikov - initial API and implementation
 *
 *******************************************************************************/
import java.io.*;
import org.codehaus.plexus.util.*;

String projectReportsPage = FileUtils.fileRead( new File( basedir, "target/site/project-reports.html" ) );
if ( projectReportsPage.indexOf( "JaCoCo Coverage Report." ) < 0 ) {
    throw new RuntimeException( "project-reports.html does not contain link to JaCoCo report" );
}

File htmlReportFile = new File( basedir, "target/site/jacoco/index.html" );
if ( !htmlReportFile.isFile() ) {
    throw new RuntimeException( "HTML report was not created" );
}

File xmlReportFile = new File( basedir, "target/site/jacoco/jacoco.xml" );
if ( !xmlReportFile.isFile() ) {
    throw new RuntimeException( "XML report was not created" );
}

File csvReportFile = new File( basedir, "target/site/jacoco/jacoco.csv" );
if ( !csvReportFile.isFile() ) {
    throw new RuntimeException( "CSV report was not created" );
}
