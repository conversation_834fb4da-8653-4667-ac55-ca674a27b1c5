<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON>, <PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>jacoco</groupId>
    <artifactId>it-report-aggregate</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>

  <artifactId>child1-test</artifactId>
  <packaging>jar</packaging>

  <dependencies>
    <dependency>
      <groupId>jacoco</groupId>
      <artifactId>child1</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>
  
</project>
