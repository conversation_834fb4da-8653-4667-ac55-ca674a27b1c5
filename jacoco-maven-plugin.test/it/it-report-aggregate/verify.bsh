/*******************************************************************************
 * Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
 * All rights reserved. This program and the accompanying materials
 * are made available under the terms of the Eclipse Public License v1.0
 * which accompanies this distribution, and is available at
 * http://www.eclipse.org/legal/epl-v10.html
 *
 * Contributors:
 *    <PERSON>, <PERSON> - initial API and implementation
 *
 *******************************************************************************/
import org.codehaus.plexus.util.*;
import java.util.regex.*;

String buildLog = FileUtils.fileRead( new File( basedir, "build.log" ) );

if ( !Pattern.compile( "Loading execution data file \\S*child1.target.jacoco.exec").matcher( buildLog ).find() ) {
    throw new RuntimeException( "Execution data from child1 was not loaded." );
}

if ( !Pattern.compile( "Loading execution data file \\S*child1-test.target.jacoco.exec").matcher( buildLog ).find() ) {
    throw new RuntimeException( "Execution data from child1-test was not loaded." );
}

if ( !new File( basedir, "child2/target/jacoco.exec" ).isFile()) {
    throw new RuntimeException( "No execution data in child2." );
}
if ( Pattern.compile( "Loading execution data file \\S*child2.target.jacoco.exec").matcher( buildLog ).find() ) {
    throw new RuntimeException( "Execution data from child2 was loaded, whereas range should exclude it." );
}
if ( !Pattern.compile( "Loading execution data file \\S*child2v2.target.jacoco.exec").matcher( buildLog ).find() ) {
    throw new RuntimeException( "Execution data from child2v2 was not loaded." );
}

if ( !Pattern.compile( "Loading execution data file \\S*report.target.jacoco.exec").matcher( buildLog ).find() ) {
    throw new RuntimeException( "Execution data from report was not loaded." );
}

File reportChild1 = new File( basedir, "report/target/site/jacoco-aggregate/child1/index.html" );
if ( !reportChild1.isFile() ) {
    throw new RuntimeException( "Report for child1 was not created." );
}

File reportChild1test = new File( basedir, "report/target/site/jacoco-aggregate/child1-test/index.html" );
if ( reportChild1test.isFile() ) {
    throw new RuntimeException( "Report for child1-test should not be created." );
}

File reportChild2 = new File( basedir, "report/target/site/jacoco-aggregate/child2/index.html" );
if ( !reportChild2.isFile() ) {
    throw new RuntimeException( "Report for child2 was not created." );
}
