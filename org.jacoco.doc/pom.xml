<?xml version="1.0" encoding="UTF-8"?>
<!--
   Copyright (c) 2009, 2019 Mountainminds GmbH & Co. KG and Contributors
   All rights reserved. This program and the accompanying materials
   are made available under the terms of the Eclipse Public License v1.0
   which accompanies this distribution, and is available at
   http://www.eclipse.org/legal/epl-v10.html

   Contributors:
      <PERSON><PERSON><PERSON> - initial API and implementation
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.jacoco</groupId>
    <artifactId>org.jacoco.build</artifactId>
    <version>0.8.4</version>
    <relativePath>../org.jacoco.build</relativePath>
  </parent>

  <artifactId>org.jacoco.doc</artifactId>
  <!-- do not set packaging to pom, because otherwise we will receive "Not executing Javadoc as the project is not a Java classpath-capable package" -->

  <name>JaCoCo :: Documentation</name>
  <description>JaCoCo Documentation</description>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.core.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.report</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.report.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent.rt</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.agent.rt.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.ant</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.ant.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.cli</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.cli.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.examples</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>org.jacoco.examples.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>jacoco-maven-plugin</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>jacoco-maven-plugin.test</artifactId>
      <version>${project.version}</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>src</sourceDirectory>

    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>${project.version}</version>
        <executions>
          <execution>
            <id>report-aggregate</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>report-aggregate</goal>
            </goals>
            <configuration>
              <title>JaCoCo</title>
              <footer>Code Coverage Report for JaCoCo ${project.version}</footer>
              <includes>
                <!-- Analyze class files only to exclude shaded agent JAR from report -->
                <include>**/*.class</include>
              </includes>
              <excludes>
                <exclude>**/HelpMojo.class</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>prepare-reports</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <echo message="JUnit report"/>
                <mkdir dir="${project.build.directory}/junit"/>
                <junitreport todir="${project.build.directory}/junit">
                  <fileset dir="../org.jacoco.agent.test/target" includes="surefire-reports/**/*.xml"/>
                  <fileset dir="../org.jacoco.agent.rt.test/target" includes="surefire-reports/**/*.xml"/>
                  <fileset dir="../org.jacoco.ant.test/target" includes="surefire-reports/**/*.xml"/>
                  <fileset dir="../org.jacoco.cli.test/target" includes="surefire-reports/**/*.xml"/>
                  <fileset dir="../org.jacoco.core.test/target" includes="surefire-reports/**/*.xml"/>
                  <fileset dir="../org.jacoco.report.test/target" includes="surefire-reports/**/*.xml"/>
                  <fileset dir="../org.jacoco.examples.test/target" includes="surefire-reports/**/*.xml"/>
                  <report format="noframes" styledir="xsl" todir="${project.build.directory}/junit">
                    <param name="qualified.bundle.version" expression="${qualified.bundle.version}"/>
                    <param name="jacoco.home.url" expression="${jacoco.home.url}"/>
                    <param name="copyright.years" expression="${copyright.years}"/>
                  </report>
                </junitreport>
                <move file="${project.build.directory}/junit/junit-noframes.html" tofile="${project.build.directory}/junit/index.html"/>
              </target>
            </configuration>
          </execution>
        </executions>
        <dependencies>
          <dependency>
            <groupId>org.apache.ant</groupId>
            <artifactId>ant-junit</artifactId>
            <version>1.8.2</version>
          </dependency>
        </dependencies>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <reportOutputDirectory>${project.build.directory}/apidocs</reportOutputDirectory>
              <includeDependencySources>true</includeDependencySources>
              <excludePackageNames>*.internal,org.jacoco.ant,org.jacoco.maven,org.jacoco.examples</excludePackageNames>
              <dependencySourceIncludes>
                <dependencySourceInclude>org.jacoco:*</dependencySourceInclude>
              </dependencySourceIncludes>
              <dependencySourceExcludes>
                <dependencySourceExclude>org.jacoco:*.test</dependencySourceExclude>
              </dependencySourceExcludes>
              <doctitle>JaCoCo ${qualified.bundle.version} API</doctitle>
              <windowtitle>JaCoCo ${qualified.bundle.version} API</windowtitle>
              <use>true</use>
              <version>false</version>
              <groups>
                <!-- Note: using org.jacoco.core.* for packages would omit the org.jacoco.core package but using org.jacoco* will include it. -->
                <group>
                  <title>Bundle org.jacoco.core</title>
                  <packages>org.jacoco.core*</packages>
                </group>
                <group>
                  <title>Bundle org.jacoco.report</title>
                  <packages>org.jacoco.report*</packages>
                </group>
                <group>
                  <title>Bundle org.jacoco.agent</title>
                  <packages>org.jacoco.agent*</packages>
                </group>
                <group>
                  <title>JaCoCo Runtime</title>
                  <packages>org.jacoco.agent.rt:com.vladium.emma.rt</packages>
                </group>
              </groups>
              <overview>javadoc/overview.html</overview>
              <stylesheetfile>javadoc/stylesheet.css</stylesheetfile>
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>xml-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>transform-generated-doc</id>
            <goals>
              <goal>transform</goal>
            </goals>
            <configuration>
              <transformationSets>
                <transformationSet>
                  <dir>../jacoco-maven-plugin/target/generated-site/xdoc</dir>
                  <includes>
                    <include>*.xml</include>
                  </includes>
                  <stylesheet>xsl/maven-goal.xsl</stylesheet>
                  <fileMappers>
                    <fileMapper implementation="org.codehaus.plexus.components.io.filemappers.FileExtensionMapper">
                      <targetExtension>.html</targetExtension>
                    </fileMapper>
                  </fileMappers>
                  <parameters>
                    <parameter>
                      <name>qualified.bundle.version</name>
                      <value>${qualified.bundle.version}</value>
                    </parameter>
                    <parameter>
                      <name>jacoco.home.url</name>
                      <value>${jacoco.home.url}</value>
                    </parameter>
                    <parameter>
                      <name>copyright.years</name>
                      <value>${copyright.years}</value>
                    </parameter>
                  </parameters>
                </transformationSet>
                <transformationSet>
                  <dir>../org.jacoco.cli/target/generated-documentation</dir>
                  <includes>
                    <include>*.xml</include>
                  </includes>
                  <stylesheet>xsl/cli.xsl</stylesheet>
                  <fileMappers>
                    <fileMapper implementation="org.codehaus.plexus.components.io.filemappers.FileExtensionMapper">
                      <targetExtension>.html</targetExtension>
                    </fileMapper>
                  </fileMappers>
                  <parameters>
                    <parameter>
                      <name>qualified.bundle.version</name>
                      <value>${qualified.bundle.version}</value>
                    </parameter>
                    <parameter>
                      <name>jacoco.home.url</name>
                      <value>${jacoco.home.url}</value>
                    </parameter>
                    <parameter>
                      <name>copyright.years</name>
                      <value>${copyright.years}</value>
                    </parameter>
                  </parameters>
                </transformationSet>
              </transformationSets>
            </configuration>
          </execution>
          <execution>
            <id>default-validate</id>
            <goals>
              <goal>validate</goal>
            </goals>
            <configuration>
              <validationSets>
                <validationSet>
                  <dir>.</dir>
                  <includes>
                    <include>docroot/**/*.html</include>
                    <include>target/generated-resources/xml/xslt/*.html</include>
                  </includes>
                </validationSet>
              </validationSets>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
