<html>
<body>

<p>
  This is the public JaCoCo API that can be used for integration. JaCoCo is
  packed as several bundles. While the bundles formally fulfill the OSGi
  specification, there is no dependency on OSGi. They can also be used as
  regular JARs in your classpath.
</p>

<h2>Bundle org.jacoco.core</h2>

<p>  
  The core bundle implements the code coverage technology itself. It provides
  APIs and implementation for
</p>

<ul>  
  <li>class file instrumentation,</li>
  <li>collecting execution data at runtime and</li> 
  <li>analyzing coverage data.</li> 
</ul>

<h2>Bundle org.jacoco.agent</h2>

<p>
  Provides the runtime Java agent (JAR file) as a resource.
</p>


<h2>Bundle org.jacoco.report</h2>

<p>
  APIs and implementation to create coverage reports in several formats.
</p>


</body>
</html>