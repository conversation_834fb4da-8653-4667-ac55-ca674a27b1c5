<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Documentation</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <span class="el_group">Documentation</span>
</div>
<div id="content"> 

<h1>Documentation</h1>

<h3>Concepts</h3>

<p>
  See what this is all about and understand the basic ideas.
</p>

<ul>
  <li><a href="mission.html">Mission</a></li>
  <li><a href="integrations.html">Integration Matrix</a></li>
  <li><i>Introduction to Code Coverage</i></li>
  <li><a href="counters.html">Coverage Counters</a></li>
</ul>

<h3>Using JaCoCo</h3>

<p>
  Use JaCoCo tools out-of-the-box.
</p>

<ul>
  <li><a href="ant.html">Ant Tasks</a></li>
  <li><a href="examples/build/build.xml">Ant Usage Example</a> -
      <a href="examples/build/build-offline.xml">Offline Example</a></li>
  <li><a href="maven.html">Maven Plug-in</a></li>
  <li><a href="examples/build/pom.xml">Maven Usage Example</a> -
      <a href="examples/build/pom-offline.xml">Offline Example</a></li>
  <li><a href="agent.html">Java Agent</a></li>
  <li><a href="cli.html">Command Line Interface</a></li>
  <li><a href="classids.html">Class Ids</a></li>
  <li><a href="offline.html">Offline Instrumentation</a></li>
  <li><a href="faq.html">FAQ</a></li>
  <li><a href="support.html">Support and Feedback</a></li>
</ul>

<h3>Integrating JaCoCo</h3>

<p>
  Integrate JaCoCo technology with your tools.
</p>

<ul>
  <li><a href="api/index.html">API JavaDoc</a></li>
  <li><a href="api.html">API Usage Examples</a></li>
  <li><a href="../coverage/report.dtd">XML Report DTD</a></li>
  <li><a href="repo.html">Maven Repository</a></li>
</ul>

<h3>Developing JaCoCo</h3>

<p>
  Improve the implementation and add new features.
</p>

<ul>
  <li><a href="environment.html">Development Environment</a></li>
  <li><a href="conventions.html">Conventions</a></li>
  <li><a href="build.html">Build</a></li>
  <li><a href="implementation.html">Implementation Design</a></li>
  <li><a href="flow.html">Control Flow Analysis</a></li>
</ul>

<h3>Miscellaneous</h3>

<ul>
  <li><a href="changes.html">Change Log</a></li>
  <li><a href="license.html">License</a></li>
  <li><a href="team.html">Team</a></li>
</ul>

</div>
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
