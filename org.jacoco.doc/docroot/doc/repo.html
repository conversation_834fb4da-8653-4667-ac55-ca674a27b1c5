<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="stylesheet" href="../coverage/jacoco-resources/prettify.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <script type="text/javascript" src="../coverage/jacoco-resources/prettify.js"></script>
  <title>JaCoCo - Maven Repository</title>
</head>
<body onload="prettyPrint()">

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Maven Repository</span>
</div>
<div id="content"> 

<h1>Maven Repository</h1>

<p>
  If you want to integrate JaCoCo within your tools you might directly retrieve
  it from the Maven repository. There are two repositories, one for JaCoCo
  releases, one for regular snapshot builds.   
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Type</td>
      <td>Repository</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Release</td>
      <td><code>http://repo1.maven.org/maven2/</code> (Central Repository)</td>
    </tr>
    <tr>
      <td>Snapshot</td>
      <td><code>https://oss.sonatype.org/content/repositories/snapshots</code></td>
    </tr>
  </tbody>
</table>

<h2>Artifacts</h2>

<p>
  Following JAR files are available:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Group ID</td>
      <td>Artifact ID</td>
      <td>Classifier</td>
      <td>Description</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>jacoco-maven-plugin</code></td>
      <td></td>
      <td>Plug-in for Maven</td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.agent</code></td>
      <td></td>
      <td>API to get a local copy of the agent</td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.agent</code></td>
      <td><code>runtime</code></td>
      <td>Agent</td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.ant</code></td>
      <td></td>
      <td>Ant Tasks</td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.ant</code></td>
      <td><code>nodeps</code></td>
      <td>Ant Tasks <i>(all dependencies included)</i></td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.cli</code></td>
      <td></td>
      <td>Command Line Interface</td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.cli</code></td>
      <td><code>nodeps</code></td>
      <td>Command Line Interface <i>(all dependencies included)</i></td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.core</code></td>
      <td></td>
      <td>Core APIs and implementations</td>
    </tr>
    <tr>
      <td><code>org.jacoco</code></td>
      <td><code>org.jacoco.report</code></td>
      <td></td>
      <td>Reporting implementation</td>
    </tr>
  </tbody>
</table>

<p>
  Please check <a href="http://search.maven.org/#search|ga|1|g%3Aorg.jacoco">here</a>
  for the latest release versions in the repository.
</p>


</div>
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
