<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Java Agent</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Java Agent</span>
</div>
<div id="content"> 

<h1>Java Agent</h1>

<p>
  JaCoCo uses class file instrumentation to record execution coverage data.
  Class files are instrumented on-the-fly using a so called Java agent. This
  mechanism allows in-memory pre-processing of all class files during class
  loading independent of the application framework.
</p>

<p class="hint">
  If you use the <a href="ant.html">JaCoCo Ant tasks</a> or
  <a href="maven.html">JaCoCo Maven plug-in</a> you don't have to care about the
  agent and its options directly. This is transparently handled by the them.
</p>

<p>
  The JaCoCo agent collects execution information and dumps it on request or
  when the JVM exits. There are three different modes for execution data output:
</p>

<ul>
  <li>File System: At JVM termination execution data is written to a local
      file.</li>
  <li>TCP Socket Server: External tools can connect to the JVM and retrieve
      execution data over the socket connection. Optional execution data reset
      and execution data dump on VM exit is possible.</li>
  <li>TCP Socket Client: At startup the JaCoCo agent connects to a given TCP
      endpoint. Execution data is written to the socket connection on request.
      Optional execution data reset and execution data dump on VM exit is
      possible.</li>
</ul>

<p>
  The agent <code>jacocoagent.jar</code> is part of the JaCoCo distribution and
  includes all required dependencies. A Java agent can be activated with the
  following JVM option: 
</p>

<pre>
  -javaagent:<i>[yourpath/]</i>jacocoagent.jar=<i>[option1]</i>=<i>[value1]</i>,<i>[option2]</i>=<i>[value2]</i>
</pre>

<p>
  The JaCoCo agent accepts the following options: 
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Option</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>destfile</code></td>
      <td>Path to the output file for execution data.</td>
      <td><code>jacoco.exec</code></td>
    </tr>
    <tr>
      <td><code>append</code></td>
      <td>If set to <code>true</code> and the execution data file already
          exists, coverage data is appended to the existing file. If set to
          <code>false</code>, an existing execution data file will be replaced.
      </td>
      <td><code>true</code></td>
    </tr>    
    <tr>
      <td><code>includes</code></td>
      <td>A list of class names that should be included in execution analysis.
          The list entries are separated by a colon (<code>:</code>) and
          may use wildcard characters (<code>*</code> and <code>?</code>).
          Except for performance optimization or technical corner cases this
          option is normally not required. 
      </td>
      <td><code>*</code> (all classes)</td>
    </tr>
    <tr>
      <td><code>excludes</code></td>
      <td>A list of class names that should be excluded from execution analysis.
          The list entries are separated by a colon (<code>:</code>) and
          may use wildcard characters (<code>*</code> and <code>?</code>).
          Except for performance optimization or technical corner cases this
          option is normally not required. 
      </td>
      <td><i>empty</i> (no excluded classes)</td>
    </tr>
    <tr>
      <td><code>exclclassloader</code></td>
      <td>A list of class loader names that should be excluded from execution
          analysis. The list entries are separated by a colon
          (<code>:</code>) and may use wildcard characters (<code>*</code> and
          <code>?</code>). This option might be required in case of special
          frameworks that conflict with JaCoCo code instrumentation, in
          particular class loaders that do not have access to the Java runtime
          classes. 
      </td>
      <td><code>sun.reflect.DelegatingClassLoader</code></td>
    </tr>
    <tr>
      <td><code>inclbootstrapclasses</code></td>
      <td>Specifies whether also classes from the bootstrap classloader should
          be instrumented. Use this feature with caution, it needs heavy
          includes/excludes tuning. 
      </td>
      <td><code>false</code></td>
    </tr>
    <tr>
      <td><code>inclnolocationclasses</code></td>
      <td>Specifies whether also classes without a source location should be
          instrumented. Normally such classes are generated at runtime e.g. by
          mocking frameworks and are therefore excluded by default. 
      </td>
      <td><code>false</code></td>
    </tr>
    <tr>
      <td><code>sessionid</code></td>
      <td>A session identifier that is written with the execution data. Without
          this parameter a random identifier is created by the agent.
      </td>
      <td><i>auto-generated</i></td>
    </tr>  
    <tr>
      <td><code>dumponexit</code></td>
      <td>If set to <code>true</code> coverage data will be written on VM
          shutdown. The dump can only be written if either <code>file</code> is
          specified or the output is <code>tcpserver</code>/<code>tcpclient</code>
          and a connection is open at the time when the VM terminates.
      </td>
      <td><code>true</code></td>
    </tr>
    <tr>
      <td><code>output</code></td>
      <td>Output method to use for writing coverage data. Valid options are:
        <ul>
          <li><code>file</code>: At VM termination execution data is written to
              the file specified in the <code>destfile</code> attribute.</li>
          <li><code>tcpserver</code>: The agent listens for incoming connections
              on the TCP port specified by the <code>address</code> and
              <code>port</code> attribute. Execution data is written to this
              TCP connection.</li>
          <li><code>tcpclient</code>: At startup the agent connects to the TCP
              port specified by the <code>address</code> and <code>port</code>
              attribute. Execution data is written to this TCP connection.</li>
          <li><code>none</code>: Do not produce any output.</li>
        </ul>
        Please see the security considerations below. 
      </td>
      <td><code>file</code></td>
    </tr>
    <tr>
      <td><code>address</code></td>
      <td>IP address or hostname to bind to when the output method is
          <code>tcpserver</code> or connect to when the output method is
          <code>tcpclient</code>. In <code>tcpserver</code> mode the value
          "<code>*</code>" causes the agent to accept connections on any local
          address.
      </td>
      <td><i>loopback interface</i></td>
    </tr>
    <tr>
      <td><code>port</code></td>
      <td>Port to bind to when the output method is <code>tcpserver</code> or
          connect to when the output method is <code>tcpclient</code>. In
          <code>tcpserver</code> mode the port must be available, which means
          that if multiple JaCoCo agents should run on the same machine,
          different ports have to be specified.
      </td>
      <td><code>6300</code></td>
    </tr>
    <tr>
      <td><code>classdumpdir</code></td>
      <td>Location relative to the working directory where all class files seen
          by the agent are dumped to. This can be useful for debugging purposes
          or in case of dynamically created classes for example when scripting
          engines are used.
      </td>
      <td><i>no dumps</i></td>
    </tr>
    <tr>
      <td><code>jmx</code></td>
      <td>If set to <code>true</code> the agent exposes
          <a href="./api/org/jacoco/agent/rt/IAgent.html">functionality</a> via
          JMX under the name <code>org.jacoco:type=Runtime</code>. Please see
          the security considerations below. 
      </td>
      <td><code>false</code></td>
    </tr>
  </tbody>
</table>

<h2>Security Consideration for Remote Agent Control</h2>

<p>
  The ports and connections opened in <code>tcpserver</code> and
  <code>tcpclient</code> mode and the JMX interface do not provide any
  authentication mechanism. If you run JaCoCo on production systems make sure
  that no untrusted sources have access to the TCP server port, or JaCoCo TCP
  clients only connect to trusted targets. Otherwise internal information of the
  application might be revealed or DOS attacks are possible. 
</p>

</div>
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
