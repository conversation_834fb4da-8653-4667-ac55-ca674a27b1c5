<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Development Environment</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Development Environment</span>
</div>
<div id="content"> 

<h1>Development Environment</h1>

<h2>Project Hosting</h2>

<p>
  The JaCoCo project is hosted on
  <a href="https://github.com/jacoco/jacoco">GitHub</a> and can be cloned from
  this URL:
</p>

<pre>
  https://github.com/jacoco/jacoco.git
</pre>

<p>
  We also track all
  <a href="https://github.com/jacoco/jacoco/issues">issues</a> and
  <a href="https://github.com/jacoco/jacoco/pulls">pull requests</a>
  on the GitHub project.
</p>


<h3>IDE</h3>

<p>
  JaCoCo development is done with the latest version of
  <a href="http://www.eclipse.org/">Eclipse</a>. Please make sure to install
  the <a href="http://www.eclipse.org/m2e/">m2e plug-in</a> to get all
  dependencies resolved properly. The source tree is organized as a list of
  Eclipse projects that can be imported in a Eclipse workspace.
</p>

<p>  
  Project specific Eclipse settings only exist for the following projects. All
  other projects and test projects link to these settings:
</p>

<ul>
  <li><code>org.jacoco.core</code></li>
  <li><code>org.jacoco.core.test</code></li>
</ul>

<p>
  These settings specify various compiler warnings and the standard Eclipse
  source formatting rules.
</p>

<h3>JRE/JDK</h3>

<p>
  The minimum supported JRE version for JaCoCo is Java 5. To guarantee
  compatibility JaCoCo release builds should always be executed using JDK 5.
  In addition we run builds with 6, 7, 8, 9, 10, 11 and 12 JDKs.
</p>

<h3>Build</h3>

<p>
  The JaCoCo build is based on <a href="http://maven.apache.org/">Maven</a>
  and requires at least Maven 3.3.9 and JDK 8.
  See the <a href="build.html">build description</a> for details.
</p>

<h3>Continuous Integration</h3>

<p>
  We have a list of CI tools directly integrated with our source repository:
</p>

<ul>
  <li><a href="https://travis-ci.org/jacoco/jacoco/">Travis CI</a></li>
  <li><a href="https://ci.appveyor.com/project/JaCoCo/jacoco">AppVeyor</a></li>
</ul>

<p>
  CI builds run for master and every pull request.
</p>

<h3>Development Build Artifacts</h3>

<p>
  Beside the <a href="http://search.maven.org/#search|ga|1|g%3Aorg.jacoco"> JaCoCo releases</a>
  the following build artifacts are automatically available during the
  development cycle:
</p>

<ul>
  <li><b>Master:</b> The current master is available through the Maven
    <a href="repo.html">snapshot repository</a>.</li>
  <li><b>Pull Requests:</b> For every pull request the
    <a href="https://ci.appveyor.com/project/JaCoCo/jacoco">AppVeyor builds</a>
    provide the corresponding all-in-one zip for download. To access the
    artifacts follow the check <code>continuous-integration/appveyor/branch</code>
    on the corresponding GitHub pull request page.</li>
</ul>

<h3>Continuous Inspection</h3>

<p>
  We track quality issues with our source code with
  <a href="https://sonarcloud.io/dashboard?id=org.jacoco:org.jacoco.build">SonarQube</a>.
</p>

<h3>Communication</h3>

<p>
  The development team communicates through a mailing list. The list is closed
  for non-developers but the archive is
  <a href="https://groups.google.com/d/forum/jacoco-dev">public</a>.
  Please use the <a href="support.html">support channels</a> to get in touch
  with the development team.
</p>

</div>
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
