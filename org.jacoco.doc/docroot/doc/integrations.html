<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Integration Matrix</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Integration Matrix</span>
</div>
<div id="content"> 

<h1>Integration Matrix</h1>

<p>
  Currently JaCoCo is integrated with the following products and technologies.
</p>

<h3>Integrations provided by the JaCoCo/EclEmma project</h3>

<table class="coverage">
  <thead>
    <tr>
      <td>Technology</td>
      <td>Documentation</td>
      <td>Remarks</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>Java API</td>
      <td><a href="api/index.html">JaCoCo JavaDoc</a></td>
      <td></td>
    </tr>
    <tr>
      <td>Java Agent</td>
      <td><a href="agent.html">JaCoCo Manual</a></td>
      <td></td>
    </tr>
    <tr>
      <td>Command Line Tools</td>
      <td><a href="cli.html">JaCoCo Manual</a></td>
      <td>Since version 0.8.0</td>
    </tr>
    <tr>
      <td>Apache Ant</td>
      <td><a href="ant.html">JaCoCo Manual</a></td>
      <td></td>
    </tr>
    <tr>
      <td>Apache Maven</td>
      <td><a href="maven.html">JaCoCo Manual</a></td>
      <td>Since version 0.5.3</td>
    </tr>
    <tr>
      <td>Eclipse</td>
      <td><a href="http://www.eclemma.org/">EclEmma Project</a></td>
      <td>Since version 2.0</td>
    </tr>
  </tbody>
</table>
   
<h3>Third-Party Integrations</h3>

<table class="coverage">
  <thead>
    <tr>
      <td>Product</td>
      <td>Remarks</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><a href="http://arquillian.org/">Arquillian</a></td>
      <td>Java EE testing framework, <a href="http://arquillian.org/modules/jacoco-extension/">JaCoCo extension</a></td>
    </tr>
    <tr>
      <td><a href="https://codecov.io/">Codecov</a></td>
      <td>Web service to track code coverage, see <a href="https://github.com/codecov/example-java">example</a></td>
    </tr>
    <tr>
      <td><a href="https://coveralls.io/">Coveralls</a></td>
      <td>Web service to track code coverage, see <a href="https://github.com/trautonen/coveralls-maven-plugin">coveralls-maven-plugin</a></td>
    </tr>
    <tr>
      <td><a href="http://www.gradle.org/">Gradle</a></td>
      <td>Build System with JaCoCo plug-in, see <a href="http://www.gradle.org/docs/current/userguide/jacoco_plugin.html">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://www.bredex.de/web/index.php/guidancer_jubula_en.html">GUIdancer</a></td>
      <td>Commercial GUI testing tool by BREDEX GmbH</td>
    </tr>
    <tr>
      <td><a href="http://www.jetbrains.com/idea/">IntelliJ IDEA</a></td>
      <td>Since version 11.1, see <a href="http://www.jetbrains.com/idea/webhelp/code-coverage.html">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://jenkins-ci.org/">Jenkins</a></td>
      <td>GSoC project of Shenyu Zheng, see <a href="https://github.com/jenkinsci/code-coverage-api-plugin">project page</a></td>
    </tr>
    <tr>
      <td><a href="http://jenkins-ci.org/">Jenkins</a></td>
      <td>GSoC project of Ognjen Bubalo, see <a href="https://wiki.jenkins-ci.org/display/JENKINS/JaCoCo+Plugin">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://java.visualstudio.com/">Visual Studio Team Services</a></td>
      <td>Cloud-powered collaboration tools by Microsoft, see <a href="https://blogs.msdn.microsoft.com/visualstudioalm/2015/11/24/testing-java-applications-with-visual-studio-team-services/">blog entry</a></td>
    </tr>
    <tr>
      <td><a href="http://netbeans.org/">NetBeans</a></td>
      <td>Since version 7.2, see <a href="http://wiki.netbeans.org/MavenCodeCoverage">documentation</a>,
          <a href="http://plugins.netbeans.org/plugin/48570/tikione-jacocoverage">plug-in</a> for Ant based projects</td>
    </tr>
    <tr>
      <td><a href="https://github.com/harrah/xsbt/wiki">sbt</a></td>
      <td>Simple Build Tool, <a href="https://bitbucket.org/jmhofer/jacoco4sbt">jacoco4sbt</a> by Joachim Hofer</td>
    </tr>
    <tr>
      <td><a href="http://www.shippable.com/">Shippable</a></td>
      <td>Continuous integration and delivery platform, see <a href="http://docs.shippable.com/ci/jacoco-reports/">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://www.sonarqube.org/">SonarQube</a></td>
      <td>Continuous inspection platform with JaCoCo support, see <a href="http://docs.sonarqube.org/display/PLUG/Code+Coverage+by+Unit+Tests+for+Java+Project">documentation</a></td>
    </tr>
    <tr>
      <td><a href="http://www.jetbrains.com/teamcity/">TeamCity</a></td>
      <td>Continuous integration server with JaCoCo support since version 8.1, see <a href="http://confluence.jetbrains.com/display/TCD8/JaCoCo">documentation</a></td>
    </tr>
    <tr>
      <td><a href="https://developer.ibm.com/urbancode/">Urban Code</a></td>
      <td>Continuous delivery platform by IBM with <a href="https://developer.ibm.com/urbancode/plugin/jacoco-3519516/">JaCoCo plug-in</a></td>
    </tr>
  </tbody>
</table>

<p>
  As <a href="license.html">always</a>, all trademarks listed above are the
  property of their respective owners.
</p>

</div>
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
