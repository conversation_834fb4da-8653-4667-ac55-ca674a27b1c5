<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="stylesheet" href="../coverage/jacoco-resources/prettify.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <script type="text/javascript" src="../coverage/jacoco-resources/prettify.js"></script>
  <title>JaCoCo - Ant Tasks</title>
</head>
<body onload="prettyPrint()">

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Ant Tasks</span>
</div>
<div id="content"> 

<h1>Ant Tasks</h1>

<p>
  JaCoCo comes with Ant tasks to launch Java programs with execution recording
  and for creating coverage reports from the recorded data. Execution data can
  be collected and managed with the tasks
  <a href="#coverage"><code>coverage</code></a>,
  <a href="#agent"><code>agent</code></a>,
  <a href="#dump"><code>dump</code></a> and
  <a href="#merge"><code>merge</code></a>. Reports in different formats are
  created with the <a href="#report"><code>report</code></a> task. For 
  <a href="offline.html">offline instrumentation</a> the task
  <a href="#instrument"><code>instrument</code></a> can be used to prepare class
  files.  
</p>

<p class="hint">
  If you want to have line number information included in the coverage reports
  or you want source code highlighting the class files of the test target must
  be compiled with debug information. 
</p>

<h2>Example</h2>

<p>
  The JaCoCo distribution contains a simple example how code coverage can be
  added to a Ant based build. The
  <a href="examples/build/build.xml">build script</a> compiles Java sources,
  runs an simple Java program and creates a coverage report. The complete
  example is located in the <code>./doc/examples/build</code> folder of the
  distribution.
</p>


<h2>Prerequisites</h2>

<p>
  The JaCoCo Ant tasks require
</p>

<ul>
  <li>Ant 1.7.0 or higher and</li>
  <li>Java 1.5 or higher (for both, the Ant runner and the test executor).</li>
</ul>

   
<p>All tasks are defined in <code>jacocoant.jar</code> (which is part of the
  distribution) and can be included in your Ant scripts with the usual
  <code>taskdef</code> declaration:   
</p>

<pre class="source lang-xml linenums">
&lt;project name="Example" xmlns:jacoco="antlib:org.jacoco.ant"&gt;

    &lt;taskdef uri="antlib:org.jacoco.ant" resource="org/jacoco/ant/antlib.xml"&gt;
        &lt;classpath path="<i>path_to_jacoco</i>/lib/jacocoant.jar"/&gt;
    &lt;/taskdef&gt;
    
    ...
    
&lt;/project&gt;
</pre>

<p>
  Alternatively you might also place the <code>jacocoant.jar</code> in your
  Ant <code><i>ANT_HOME</i>/lib</code> folder. If you use the name space URI
  <code>antlib:org.jacoco.ant</code> for JaCoCo tasks Ant will find them
  automatically without the <code>taskdef</code> declaration above.
</p>

<p class="hint">
  Declaring a XML namespace for JaCoCo tasks is optional but always recommended
  if you mix tasks from different libraries. All subsequent examples use the
  <code>jacoco</code> prefix declared above. If you don't declare a separate
  namespace the <code>jacoco</code> prefix must be removed from the following
  examples.
</p>

<h2><a name="coverage">Task <code>coverage</code></a></h2>

<p>
  The standard Ant tasks to launch Java programs are <code>java</code>, <code>junit</code> and
  <code>testng</code>. To add code coverage recording to these tasks they can
  simply be wrapped with the <code>coverage</code> task as shown in the
  following examples:  
</p>

<pre class="source lang-xml linenums">
&lt;jacoco:coverage>
    &lt;java classname="org.jacoco.examples.HelloJaCoCo" fork="true"&gt;
        &lt;classpath&gt;
            &lt;pathelement location="./bin"/&gt;
        &lt;/classpath&gt;
    &lt;/java&gt;
&lt;/jacoco:coverage&gt;


&lt;jacoco:coverage>
    &lt;junit fork="true" forkmode="once"&gt;
        &lt;test name="org.jacoco.examples.HelloJaCoCoTest"/&gt;
        &lt;classpath&gt;
            &lt;pathelement location="./bin"/&gt;
        &lt;/classpath&gt;
    &lt;/junit&gt;
&lt;/jacoco:coverage&gt;
</pre>

<p>
  Resulting coverage information is collected during execution and written
  to a file when the process terminates. Note the <code>fork</code> attribute
  above in the wrapped <code>java</code> task. 
</p>

<p class="hint">
  The nested task always has to declare <code>fork="true"</code>, otherwise the
  <code>coverage</code> task can't record coverage information and will fail.
  In addition the <code>junit</code> task should declare
  <code>forkmode="once"</code> to avoid starting a new JVM for every single test
  case and decreasing execution performance dramatically (unless this is
  required by the nature of the test cases). Note that
  <code>forkmode="perTest"</code> or <code>forkmode="perBatch"</code> should not
  be combined with <code>append="false"</code> as the execution data file is
  overwritten with the execution of every test.
</p>

<p>
  The coverage task must wrap exactly one task. While it typically works without
  any configuration, the behavior can be adjusted with some optional attributes: 
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>enabled</code></td>
      <td>If set to <code>true</code> coverage data will be collected for the contained task.</td>
      <td><code>true</code></td>
    </tr>
    <tr>
      <td><code>destfile</code></td>
      <td>Path to the output file for execution data.</td>
      <td><code>jacoco.exec</code></td>
    </tr>
    <tr>
      <td><code>append</code></td>
      <td>If set to <code>true</code> and the execution data file already
          exists, coverage data is appended to the existing file. If set to
          <code>false</code>, an existing execution data file will be replaced.
      </td>
      <td><code>true</code></td>
    </tr>    
    <tr>
      <td><code>includes</code></td>
      <td>A list of class names that should be included in execution analysis.
          The list entries are separated by a colon (<code>:</code>) and
          may use wildcard characters (<code>*</code> and <code>?</code>).
          Except for performance optimization or technical corner cases this
          option is normally not required. 
      </td>
      <td><code>*</code> (all classes)</td>
    </tr>
    <tr>
      <td><code>excludes</code></td>
      <td>A list of class names that should be excluded from execution analysis.
          The list entries are separated by a colon (<code>:</code>) and
          may use wildcard characters (<code>*</code> and <code>?</code>).
          Except for performance optimization or technical corner cases this
          option is normally not required. 
      </td>
      <td><i>empty</i> (no excluded classes)</td>
    </tr>
    <tr>
      <td><code>exclclassloader</code></td>
      <td>A list of class loader names, that should be excluded from execution
          analysis. The list entries are separated by a colon
          (<code>:</code>) and may use wildcard characters (<code>*</code> and
          <code>?</code>). This option might be required in case of special
          frameworks that conflict with JaCoCo code instrumentation, in
          particular class loaders that do not have access to the Java runtime
          classes. 
      </td>
      <td><code>sun.reflect.DelegatingClassLoader</code></td>
    </tr>
    <tr>
      <td><code>inclbootstrapclasses</code></td>
      <td>Specifies whether also classes from the bootstrap classloader should
          be instrumented. Use this feature with caution, it needs heavy
          includes/excludes tuning. 
      </td>
      <td><code>false</code></td>
    </tr>
    <tr>
      <td><code>inclnolocationclasses</code></td>
      <td>Specifies whether also classes without a source location should be
          instrumented. Normally such classes are generated at runtime e.g. by
          mocking frameworks and are therefore excluded by default. 
      </td>
      <td><code>false</code></td>
    </tr>
    <tr>
      <td><code>sessionid</code></td>
      <td>A session identifier that is written with the execution data. Without
          this parameter a random identifier is created by the agent.
      </td>
      <td><i>auto-generated</i></td>
    </tr>  
    <tr>
      <td><code>dumponexit</code></td>
      <td>If set to <code>true</code> coverage data will be written on VM
          shutdown.
      </td>
      <td><code>true</code></td>
    </tr>
    <tr>
      <td><code>output</code></td>
      <td>Output method to use for writing coverage data. Valid options are:
        <ul>
          <li><code>file</code>: At VM termination execution data is written to
              the file specified in the <code>destfile</code> attribute.</li>
          <li><code>tcpserver</code>: The agent listens for incoming connections
              on the TCP port specified by the <code>address</code> and
              <code>port</code> attribute. Execution data is written to this
              TCP connection.</li>
          <li><code>tcpclient</code>: At startup the agent connects to the TCP
              port specified by the <code>address</code> and <code>port</code>
              attribute. Execution data is written to this TCP connection.</li>
          <li><code>none</code>: Do not produce any output.</li>
        </ul>
      </td>
      <td><code>file</code></td>
    </tr>
    <tr>
      <td><code>address</code></td>
      <td>IP address or hostname to bind to when the output method is
          <code>tcpserver</code> or connect to when the output method is
          <code>tcpclient</code>.  In <code>tcpserver</code> mode the value
          "<code>*</code>" causes the agent to accept connections on any local
          address.
      </td>
      <td><i>loopback interface</i></td>
    </tr>
    <tr>
      <td><code>port</code></td>
      <td>Port to bind to when the output method is <code>tcpserver</code> or
          connect to when the output method is <code>tcpclient</code>. In
          <code>tcpserver</code> mode the port must be available, which means
          that if multiple JaCoCo agents should run on the same machine,
          different ports have to be specified.
      </td>
      <td><code>6300</code></td>
    </tr>
    <tr>
      <td><code>classdumpdir</code></td>
      <td>Location relative to the working directory where all class files seen
          by the agent are dumped to. This can be useful for debugging purposes
          or in case of dynamically created classes for example when scripting
          engines are used.
      </td>
      <td><i>no dumps</i></td>
    </tr>
    <tr>
      <td><code>jmx</code></td>
      <td>If set to <code>true</code> the agent exposes
          <a href="./api/org/jacoco/agent/rt/IAgent.html">functionality</a> via
          JMX under the name <code>org.jacoco:type=Runtime</code>.
      </td>
      <td><code>false</code></td>
    </tr>
  </tbody>
</table>


<h2><a name="agent">Task <code>agent</code></a></h2>

<p>
  If the <code>coverage</code> task is not suitable for your launch target, you
  might alternatively use the <code>agent</code> task to create the
  <a href="agent.html">Java agent</a> parameter. The following example defines a
  Ant property with the name <code>agentvmparam</code> that can be directly used
  as a Java VM parameter:
</p>

<pre class="source lang-xml linenums">
&lt;jacoco:agent property="agentvmparam"/&gt;
</pre>

<p>
  This task has the same attributes as the <code>coverage</code> task plus an
  additional property to specify the target property name:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>enabled</code></td>
      <td>When this variable is set to <code>false</code> the value of <code>property</code> will be set to an empty string, effectively
          disabling coverage instrumentation for any tasks that used the value.</td>
      <td><code>true</code></td>
    </tr>
    <tr>
      <td><code>property</code></td>
      <td>Name of the Ant property to set.</td>
      <td><i>none (required)</i></td>
    </tr>
    <tr>
      <td colspan="3"><i>All attributes of the <code>coverage</code> task.</i></td>
    </tr>
  </tbody>
</table>


<h2><a name="dump">Task <code>dump</code></a></h2>

<p>
  This task allows to remotely collect execution data from another JVM without
  stopping it. For example:
</p>

<pre class="source lang-xml linenums">
&lt;jacoco:dump address="server.example.com" reset="true" destfile="remote.exec"/&gt;
</pre>

<p>
  Remote dumps are usefull for long running Java processes like application
  servers.
</p>

<p class="hint">
  The target JVM needs to have a <a href="agent.html">JaCoCo agent</a>
  configured with <code>output</code> mode <code>tcpserver</code>. See
  <a href="#coverage"><code>coverage</code></a> and
  <a href="#agent"><code>agent</code></a> tasks above.
</p>

<p>
  The <code>dump</code> task has the following attributes: 
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>address</code></td>
      <td>Target IP address or DNS name.</td>
      <td><code>localhost</code></td>
    </tr>
    <tr>
      <td><code>port</code></td>
      <td>Target TCP port.</td>
      <td><code>6300</code></td>
    </tr>
    <tr>
      <td><code>retryCount</code></td>
      <td>Number of retries which the goal will attempt to establish a
          connection. This can be used to wait until the target JVM is
          successfully launched.</td>
      <td><code>10</code></td>
    </tr>
    <tr>
      <td><code>dump</code></td>
      <td>Flag whether execution data should be dumped.</td>
      <td><code>true</code></td>
    </tr>
    <tr>
      <td><code>reset</code></td>
      <td>Flag whether execution data should be reset in the target agent after
          the dump.</td>
      <td><code>false</code></td>
    </tr>
    <tr>
      <td><code>destfile</code></td>
      <td>File location to write the collected execution data to.</td>
      <td><i>none (required if dump=true)</i></td>
    </tr>
    <tr>
      <td><code>append</code></td>
      <td>If set to <code>true</code> and the execution data file already
          exists, coverage data is appended to the existing file. If set to
          <code>false</code>, an existing execution data file will be replaced.
      </td>
      <td><code>true</code></td>
    </tr>
  </tbody>
</table>


<h2><a name="merge">Task <code>merge</code></a></h2>

<p>
  This task can be used to merge the execution data from multiple test runs
  into a single data store. 
</p>

<pre class="source lang-xml linenums">
&lt;jacoco:merge destfile="merged.exec"&gt;
    &lt;fileset dir="executionData" includes="*.exec"/&gt;
&lt;/jacoco:merge&gt;
</pre>

<p>
  The task definition can contain any number of resource collection types and
  has the following mandatory attribute: 
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>destfile</code></td>
      <td>File location to write the merged execution data to.</td>
      <td><i>none (required)</i></td>
    </tr>
  </tbody>
</table>


<h2><a name="report">Task <code>report</code></a></h2>

<p>
  Finally different reports can be created with the <code>report</code> task.
  A report task declaration consists of different sections, two specify the
  input data, additional ones specify the output formats:  
</p>

<pre class="source lang-xml linenums">
&lt;jacoco:report&gt;
			
    &lt;executiondata&gt;
        &lt;file file="jacoco.exec"/&gt;
    &lt;/executiondata&gt;
			
    &lt;structure name="Example Project"&gt;
        &lt;classfiles&gt;
            &lt;fileset dir="classes"/&gt;
        &lt;/classfiles&gt;
        &lt;sourcefiles encoding="UTF-8"&gt;
            &lt;fileset dir="src"/&gt;
        &lt;/sourcefiles&gt;
    &lt;/structure&gt;
			
    &lt;html destdir="report"/&gt;
			
&lt;/jacoco:report&gt;
</pre>

<p>
  As you can see from the example above the <code>report</code> task is based
  on several nested elements:
</p>

<h3>Element <code>executiondata</code></h3>

<p>
  Within this element Ant resources and resource collections can be specified,
  that represent JaCoCo execution data files. If more than one execution data
  file is specified, execution data is combined. A particular piece of code is
  considered executed when it is marked as such in any of the input files.
</p>

<h3>Element <code>structure</code></h3>

<p>
  This element defines the report structure. It might contain the following
  nested elements:
</p>

<ul>
  <li><code>classfiles</code>: Container element for Ant resources and resource
    collections that can specify Java class files, archive files (jar, war, ear
    etc. or Pack200) or folders containing class files. Archives and folders are
    searched recursively for class files.</li>
  <li><code>sourcefiles</code>: Optional container element for Ant resources and
    resource collections that specify corresponding source files. If source
    files are specified, some report formats include highlighted source code.
    Source files can be specified as individual files or as source directories.</li>
</ul>

<p>
  The <code>sourcefiles</code> element has these optional attributes:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>encoding</code></td>
      <td>Character encoding of the source files.</td>
      <td>Platform default encoding</td>
    </tr>
    <tr>
      <td><code>tabwidth</code></td>
      <td>Number of whitespace characters that represent a tab character.</td>
      <td>4 characters</td>
    </tr>
  </tbody>
</table>

<p class="hint">
  <b>Important:</b> Source file resources must always be specified relative to
  the respective source folder. If directory resources are given, they must
  directly point to source folders. Otherwise source lookup will not succeed.
</p>

<p>
  Note that the <code>classfiles</code> and <code>sourcefiles</code> elements
  accept any
  <a href="http://ant.apache.org/manual/Types/resources.html#collection">Ant
  resource collection</a>. Therefore also filtering the class file set is
  possible and allows to narrow the scope of the report, for example: 
</p>

<pre class="source lang-xml linenums">
&lt;classfiles&gt;
    &lt;fileset dir="classes"&gt;
        &lt;include name="org/jacoco/examples/important/**/*.class"/&gt;
    &lt;/fileset&gt;
&lt;/classfiles&gt;
</pre>

<p class="hint">
  <b>Performance Warning:</b> Although it is technically possible and sometimes
  convenient to use Ant's <code>zipfileset</code> to specify class or source
  files, this resource type has poor performance characteristics and comes with
  an huge memory overhead especially for large scale projects.
</p>

<p>
  The structure can be refined with a hierarchy of <code>group</code> elements.
  This way the coverage report can reflect different modules of a software
  project. For each group element the corresponding class and source files can
  be specified separately. For example: 
</p>

<pre class="source lang-xml linenums">
&lt;structure name="Example Project"&gt;
    &lt;group name="Server"&gt;
        &lt;classfiles&gt;
            &lt;fileset dir="${workspace.dir}/org.jacoco.example.server/classes"/&gt;
        &lt;/classfiles&gt;
        &lt;sourcefiles&gt;
            &lt;fileset dir="${workspace.dir}/org.jacoco.example.server/src"/&gt;
        &lt;/sourcefiles&gt;
    &lt;/group&gt;
    &lt;group name="Client"&gt;
        &lt;classfiles&gt;
            &lt;fileset dir="${workspace.dir}/org.jacoco.example.client/classes"/&gt;
        &lt;/classfiles&gt;
        &lt;sourcefiles&gt;
            &lt;fileset dir="${workspace.dir}/org.jacoco.example.client/src"/&gt;
        &lt;/sourcefiles&gt;
    &lt;/group&gt;
    
    ...
    
&lt;/structure&gt;
</pre>

<p>
  Both <code>structure</code> and <code>group</code> elements have the following
  mandatory attribute:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>name</code></td>
      <td>Name of the structure or group.</td>
      <td><i>none (required)</i></td>
    </tr>
  </tbody>
</table>

<h3>Element <code>html</code></h3>

<p>
  Create a multi-page report in HTML format. The report can either be written as
  multiple files into a directory or compressed into a single ZIP file.
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>destdir</code></td>
      <td>Directory to create the report in. Either this property or
        <code>destfile</code> has to be supplied.</td>
      <td><i>none (required)</i></td>
    </tr>
    <tr>
      <td><code>destfile</code></td>
      <td>Zip file to create the report in.  Either this property or
        <code>destdir</code> has to be supplied.</td>
      <td><i>none (required)</i></td>
    </tr>
    <tr>
      <td><code>footer</code></td>
      <td>Footer text for each report page.</td>
      <td><i>no footer</i></td>
    </tr>
    <tr>
      <td><code>encoding</code></td>
      <td>Character encoding of generated HTML pages.</td>
      <td><code>UTF-8</code></td>
    </tr>
    <tr>
      <td><code>locale</code></td>
      <td>Locale specified as ISO code (en, fr, jp, ...) used for number
      formatting. Locale country and variant can be separated with an underscore
      (de_CH).</td>
      <td><i>platform locale</i></td>
    </tr>
  </tbody>
</table>

<h3>Element <code>xml</code></h3>

<p>
  Create a single-file report in XML format. 
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>destfile</code></td>
      <td>Location to write the report file to.</td>
      <td><i>none (required)</i></td>
    </tr>
    <tr>
      <td><code>encoding</code></td>
      <td>Encoding of the generated XML document.</td>
      <td><code>UTF-8</code></td>
    </tr>
  </tbody>
</table>

<h3>Element <code>csv</code></h3>

<p>
  Create single-file report in CSV format. 
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>destfile</code></td>
      <td>Location to write the report file to.</td>
      <td><i>none (required)</i></td>
    </tr>
    <tr>
      <td><code>encoding</code></td>
      <td>Encoding of the generated CSV document.</td>
      <td><code>UTF-8</code></td>
    </tr>
  </tbody>
</table>

<h3>Element <code>check</code></h3>

<p>
  This report type does not actually create a report. It checks coverage
  counters and reports violations of configured rules. Every rule is applied to
  elements of a given type (class, package, bundle, etc.) and has a list of
  limits which are checked for every element. The following example checks that
  for every package the line coverage is at least 80% and no class is missed:
</p>

<pre class="source lang-xml linenums">
&lt;check&gt;
    &lt;rule element="PACKAGE"&gt;
        &lt;limit counter="LINE" value="COVEREDRATIO" minimum="80%"/&gt;
        &lt;limit counter="CLASS" value="MISSEDCOUNT" maximum="0"/&gt;
    &lt;/rule&gt;
&lt;/check&gt;
</pre>

<p>
  The <code>check</code> element has the following attributes:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>rules</code></td>
      <td>List of rules to check.</td>
      <td><i>none</i></td>
    </tr>
    <tr>
      <td><code>failonviolation</code></td>
      <td>Specifies whether build should fail in case of rule violations.</td>
      <td><code>true</code></td>
    </tr>
    <tr>
      <td><code>violationsproperty</code></td>
      <td>The name of an Ant property which is filled with the violation
          messages.</td>
      <td><i>none</i></td>
    </tr>
  </tbody>
</table>

<p>
  Within the <code>check</code> element any number of <code>rule</code> elements
  can be nested:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>element</code></td>
      <td>The elements this rule applies to. Possible values are
          <code>BUNDLE</code>, <code>PACKAGE</code>, <code>CLASS</code>,
          <code>SOURCEFILE</code> and <code>METHOD</code>.</td>
      <td><code>BUNDLE</code></td>
    </tr>
    <tr>
      <td><code>includes</code></td>
      <td>A list of element names that should be checked. The list entries are
          separated by a colon (:) and may use wildcard characters (* and ?).</td>
      <td><code>*</code></td>
    </tr>
    <tr>
      <td><code>excludes</code></td>
      <td>A list of element names that should not be checked. The list entries
          are separated by a colon (:) and may use wildcard characters (* and ?).</td>
      <td><i>empty (no excludes)</i></td>
    </tr>
    <tr>
      <td><code>limits</code></td>
      <td>List of limits to check.</td>
      <td><i>none</i></td>
    </tr>
  </tbody>
</table>

<p>
  Within the <code>rule</code> element any number of <code>limit</code> elements
  can be nested:
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>counter</code></td>
      <td>The <a href="counters.html">counter</a> which should be checked.
          Possible options are <code>INSTRUCTION</code>, <code>LINE</code>,
          <code>BRANCH</code>, <code>COMPLEXITY</code>, <code>METHOD</code> and
          <code>CLASS</code>.</td>
      <td><code>INSTRUCTION</code></td>
    </tr>
    <tr>
      <td><code>value</code></td>
      <td>The counter value that should be checked. Possible options are
      <code>TOTALCOUNT</code>, <code>MISSEDCOUNT</code>,
      <code>COVEREDCOUNT</code>, <code>MISSEDRATIO</code> and
      <code>COVEREDRATIO</code>.</td>
      <td><code>COVEREDRATIO</code></td>
    </tr>
    <tr>
      <td><code>minimum</code></td>
      <td>Expected minimum value. If the minimum refers to a ratio the range is
          from 0.0 to 1.0 where the number of decimal places will also determine
          the precision in error messages. A limit ratio may optionally be
          declared as a percentage where 0.80 and 80% represent the same value,
          the value must end with %.</td>
      <td><i>none</i></td>
    </tr>
    <tr>
      <td><code>maximum</code></td>
      <td>Expected maximum value, see <code>minimum</code> for details.</td>
      <td><i>none</i></td>
    </tr>
  </tbody>
</table>

<h2><a name="instrument">Task <code>instrument</code></a></h2>

<p class="hint">
  <b>Warning:</b> The preferred way for code coverage analysis with JaCoCo is
  on-the-fly instrumentation. Offline instrumentation has several drawbacks and
  should only be used if a specific scenario explicitly requires this mode.
  Please consult <a href="offline.html">documentation</a> about offline
  instrumentation before using this mode.
</p>

<p>
  This task is used for <a href="offline.html">offline instrumentation</a> of
  class files. The task takes a set of files and writes instrumented
  versions to a specified location. The task takes any file type as input. Java
  class files are instrumented. Archives (jar, war, ear etc. or Pack200) are
  searched recursively for class files which then get instrumented. All other
  files are copied without modification.
</p>

<pre class="source lang-xml linenums">
&lt;jacoco:instrument destdir="target/classes-instr"&gt;
    &lt;fileset dir="target/classes" includes="**/*.class"/&gt;
&lt;/jacoco:instrument&gt;
</pre>

<p>
  The task definition can contain any number of resource collection types and
  has the following mandatory attribute: 
</p>

<table class="coverage">
  <thead>
    <tr>
      <td>Attribute</td>
      <td>Description</td>
      <td>Default</td>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>destdir</code></td>
      <td>Directory location to write the instrumented files to.</td>
      <td><i>none (required)</i></td>
    </tr>
    <tr>
      <td><code>removesignatures</code></td>
      <td>If set to <code>true</code> all signature related information is 
          stripped from JARs. This is typically necessary as instrumentation
          breaks the signatures of the original class files.</td>
      <td><code>true</code></td>
    </tr>
  </tbody>
</table>

</div>
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
