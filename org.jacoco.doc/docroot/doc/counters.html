<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - Coverage Counter</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">Coverage Counters</span>
</div>
<div id="content"> 

<h1>Coverage Counters</h1>

<p>
  JaCoCo uses a set of different counters to calculate coverage metrics. All
  these counters are derived from information contained in Java class files
  which basically are Java byte code instructions and debug information 
  optionally embedded in class files. This approach allows efficient on-the-fly
  instrumentation and analysis of applications even when no source code is
  available. In most cases the collected information can be mapped back to
  source code and visualized down to line level granularity. Anyhow there are
  limitations to this approach. The class files have to be compiled with debug
  information to calculate line level coverage and provide source highlighting.
  Not all Java language constructs can be directly compiled to corresponding
  byte code. In such cases the Java compiler creates so called <i>synthetic</i>
  code which sometimes results in unexpected code coverage results. 
</p>

<h2>Instructions (C0 Coverage)</h2>

<p>
  The smallest unit JaCoCo counts are single Java byte code instructions.
  <i>Instruction coverage</i> provides information about the amount of code that
  has been executed or missed. This metric is completely independent from source
  formatting and always available, even in absence of debug information in the
  class files.
</p>

<h2>Branches (C1 Coverage)</h2>

<p>
  JaCoCo also calculates <i>branch coverage</i> for all <code>if</code> and
  <code>switch</code> statements. This metric counts the total number of such
  branches in a method and determines the number of executed or missed branches.
  Branch coverage is always available, even in absence of debug information in
  the class files. Note that exception handling is not considered as branches
  in the context of this counter definition.
</p>

<p>
  If the class files haven been compiled with debug information decision points
  can be mapped to source lines and highlighted accordingly:
</p>

<ul>
  <li>No coverage: No branches in the line has been executed (red diamond)</li>
  <li>Partial coverage: Only a part of the branches in the line have been
      executed (yellow diamond)</li>
  <li>Full coverage: All branches in the line have been executed (green diamond)</li>
</ul>

<h2>Cyclomatic Complexity</h2>

<p>
  JaCoCo also calculates cyclomatic complexity for each non-abstract method and
  summarizes complexity for classes, packages and groups. According to its
  definition by
  <a href="http://hissa.nist.gov/HHRFdata/Artifacts/ITLdoc/235/title.htm">McCabe1996</a>
  cyclomatic complexity is the minimum number of paths that can, in (linear)
  combination, generate all possible paths through a method. Thus the
  complexity value can serve as an indication for the number of unit test cases
  to fully cover a certain piece of software. Complexity figures can always be
  calculated, even in absence of debug information in the class files.
</p>

<p>
  The formal definition of the cyclomatic complexity v(G) is based on the
  representation of a method's control flow graph as a directed graph:  
</p>

<blockquote>
  <p>
  v(G) = E - N + 2
  </p>
</blockquote>

<p>
  Where E is the number of edges and N the number of nodes. JaCoCo calculates
  cyclomatic complexity of a method with the following equivalent equation based
  on the number of branches (B) and the number of decision points (D):
</p>

<blockquote>
  <p>
  v(G) = B - D + 1
  </p>
</blockquote>

<p>
  Based on the coverage status of each branch JaCoCo also calculates covered and
  missed complexity for each method. Missed complexity again is an indication
  for the number of test cases missing to fully cover a module. Note that as
  JaCoCo does not consider exception handling as branches try/catch blocks will
  also not increase complexity.
</p>

<h2>Lines</h2>

<p>
  For all class files that have been compiled with debug information, coverage
  information for individual lines can be calculated. A source line is
  considered executed when at least one instruction that is assigned to this
  line has been executed.
</p>

<p>
  Due to the fact that a single line typically compiles to multiple byte code
  instructions the source code highlighting shows three different status for
  each line containing source code: 
</p>

<ul>
  <li>No coverage: No instruction in the line has been executed (red
      background)</li>
  <li>Partial coverage: Only a part of the instruction in the line have been
      executed (yellow background)</li>
  <li>Full coverage: All instructions in the line have been executed (green
      background)</li>
</ul>

<p>
  Depending on source formatting a single line of a source code may refer to
  multiple methods or multiple classes. Therefore the line count of methods
  cannot be simply added to obtain the total number for the containing class.
  The same holds true for the lines of multiple classes within a single source
  file. JaCoCo calculates line coverage for classes and source file based on the
  actual source lines covered.
</p>

<h2>Methods</h2>

<p>
  Each non-abstract method contains at least one instruction. A method is
  considered as executed when at least one instruction has been executed. As
  JaCoCo works on byte code level also constructors and static initializers are
  counted as methods. Some of these methods may not have a direct correspondence
  in Java source code, like implicit and thus generated default constructors or
  initializers for constants.
</p>

<h2>Classes</h2>

<p>
  A class is considered as executed when at least one of its methods has been
  executed. Note that JaCoCo considers constructors as well as static
  initializers as methods. As Java interface types may contain static
  initializers such interfaces are also considered as executable classes.
</p>

</div> 
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
