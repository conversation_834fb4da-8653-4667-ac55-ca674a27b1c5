<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
  <link rel="stylesheet" href="resources/doc.css" charset="UTF-8" type="text/css" />
  <link rel="shortcut icon" href="resources/report.gif" type="image/gif" />
  <title>JaCoCo - FAQ</title>
</head>
<body>

<div class="breadcrumb">
  <a href="../index.html" class="el_report">JaCoCo</a> &gt;
  <a href="index.html" class="el_group">Documentation</a> &gt;
  <span class="el_source">FAQ</span>
</div>
<div id="content"> 

<h1>FAQ</h1>

<p>
  This is a compilation of questions that have been asked by JaCoCo users
  before.
</p>

<h3>When will feature X be released?</h3>
<p>
  JaCoCo is maintained by volunteers in their free time. Since we cannot
  guarantee free capacity, we do not commit to particular release dates.
  Typically, you can expect a couple of releases every year.
</p>
<p>  
  In the <a href="changes.html">change log</a>, you can see all features
  that have been been implemented in master branch and will be available
  with the next release. And in the meantime you can test latest build of
  of master branch (<a href="repo.html">Maven SNAPSHOT</a>) and provide
  <a href="support.html">feedback</a> to us.
</p>

<h3>Does JaCoCo have a plug-in for [Eclipse|Netbeans|Whatever...]?</h3>
<p>
  See <a href="integrations.html">this list</a> for current integrations with
  various tools.
</p>

<h3>What Java versions are supported by JaCoCo?</h3>
<p>
  JaCoCo supports Java class files from version 1.0 to 12. However the minimum
  JRE version required by the JaCoCo runtime (e.g. the agent) and the JaCoCo
  tools is 1.5. Also note that class files under test from version 1.6 and above
  have to contain valid stackmap frames.
</p>

<h3>Why do I get the error "Can't add different class with same name"?</h3>
<p>
  For coverage report generation all classes within a group must have unique
  names. You get this error during report generation if JaCoCo is supplied with
  multiple different class files with the same name. To fix this remove those
  duplicate classes or create separate reports or report groups for each version.
</p>

<h3>Source code lines with exceptions show no coverage. Why?</h3>
<p>
  JaCoCo determines code execution with so called probes. Probes are inserted
  into the control flow at certain positions. Code is considered as executed
  when a subsequent probe has been executed. In case of exceptions such a
  sequence of instructions is aborted somewhere in the middle and the
  corresponding line of source code is not marked as covered.
</p>

<h3>Why does the coverage report not show line coverage figures?</h3>
<p>
  JaCoCo is based on class files analysis. To calculate line coverage class
  files must contain line number attributes. For this your code must be compiled
  with debug information.
</p>

<h3>Why does the coverage report not show highlighted source code?</h3>
<p>
  Make sure the following prerequisites are fulfilled to get source code
  highlighting in JaCoCo coverage reports:
</p>
<ul>
  <li>Class files must be compiled with debug information to contain line numbers.</li>
  <li>Source files must be properly supplied at report generation time. I.e.
      specified source folders must be the direct parent of the folders that
      define the Java packages.</li>
</ul>

<h3>Why does a class show as not covered although it has been executed?</h3>
<p>
  First make sure execution data has been collected. For this select the
  <i>Sessions</i> link on the top right corner of the HTML report and check
  whether the class in question is listed. If it is listed but not linked the
  class at execution time is a different class file. Make sure you're using the
  exact same class file at runtime as for report generation. Note that some
  tools (e.g. EJB containers, mocking frameworks) might modify your class files
  at runtime. Please see the chapter about <a href="classids.html"> class
  ids</a> for a detailed discussion.
</p>

<h3>Why are abstract methods not shown in coverage reports?</h3>
<p>
  Abstract methods do not contain code, therefore code coverage cannot be
  evaluated. Indeed code coverage is recorded for subclasses implementing these
  methods. The same applies to non-default methods in interfaces.
</p>

<h3>Can I collect coverage information without stopping the JVM?</h3>
<p>
  Yes, there are three possible ways: The JaCoCo <a href="agent.html">agent</a>
  can be configured for remote control via TCP/IP sockets. This allows to
  collect execution data at any point in time from a running JVM. The
  <a href="ant.html#dump"><code>dump</code> Ant task</a>, the
  <a href="dump-mojo.html"><code>dump</code> Maven goal</a> and the
  <a href="cli.html">command line interface</a> can be used to request dumps.
  The remote control feature also allows you to reset execution data.
</p>
<p>
  Alternatively the JaCoCo <a href="agent.html">agent</a> can be configured to
  expose some functionality via JMX (<code>jmx=true</code>). The bean
  <code><a href="./api/org/jacoco/agent/rt/IAgent.html">org.jacoco:type=Runtime</a></code>
  provides operations to dump and reset execution data at any point in time.
</p>
<p>
  In addition JaCoCo provides a <a href="./api/org/jacoco/agent/rt/RT.html">Java API</a>
  to directly access the runtime within the JVM executing the tests.
</p>

<h3>My code uses reflection. Why does it fail when I execute it with JaCoCo?</h3>
<p>
  To collect execution data JaCoCo instruments the classes under test which adds
  two members to the classes: A private static field <code>$jacocoData</code>
  and a private static method <code>$jacocoInit()</code>. Both members are
  marked as synthetic.
</p>
<p>
  Please change your code to ignore synthetic members. This is a good practice
  anyways as also the Java compiler creates synthetic members in certain
  situation.
</p>

<h3>Why do I get an error while instrumenting certain Java classes?</h3>
<p>
  JaCoCo can instrument valid class files only. Class files with syntactical or
  semantical error will typically lead to exceptions. In certain areas JaCoCo is
  more restrictive then the JVM: JaCoCo expects valid so-called "stackmap
  frame" information in class files of version 1.6 or higher. In turn JaCoCo
  instrumented classes are expected to conform the specification.
</p>
<p>
  Invalid class files are typically created by some frameworks which do not
  properly adjust stackmap frames when manipulating bytecode.
</p>

<h3>Does JaCoCo run on the IBM JRE J9 and WebSphere?</h3>
<p>
  It does. To make the JaCoCo agent work please set the VM option
  <code>-Xshareclasses:none</code>.
</p>

<h3>Why do I get a <code>NoClassDefFoundError</code> or
    <code>ClassNotFoundException</code> for class
    <code>org.jacoco.agent[...]Offline</code>?</h3>
<p>
  If you use <a href="offline.html">offline instrumentation</a> the instrumented
  classes get a direct dependency on the JaCoCo runtime. Therefore
  <code>jacocoagent.jar</code> of the same JaCoCo version must be on the
  classpath and accessible from by the instrumented classes. 
</p>

<h3>Why do I get a <code>StackOverflowError</code> during code coverage analysis?</h3>
<p>
  There are two known reasons for this:
</p>
<ul>
  <li>Misconfiguration: If you configure two JaCoCo agents of different releases
      they will instrument each other and cause a endless recursion. Check the
      effective java command line and avoid such configurations.</li>
  <li>Heavy stack usage: JaCoCo instrumentation adds a small runtime overhead
      by adding a local variable to each method. If your application is already
      close to the maximum stack size this can eventually lead to an
      <code>StackOverflowError</code>. Increase the maximum java stack size with
      the <code>-Xss</code> JVM option.</li>
</ul>

</div>
<div class="footer">
  <span class="right"><a href="@jacoco.home.url@">JaCoCo</a> @qualified.bundle.version@</span>
  <a href="license.html">Copyright</a> &copy; @copyright.years@ Mountainminds GmbH &amp; Co. KG and Contributors
</div>

</body>
</html>
